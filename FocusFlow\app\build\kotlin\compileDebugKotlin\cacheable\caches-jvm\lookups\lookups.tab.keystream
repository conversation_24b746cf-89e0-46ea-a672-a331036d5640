  R android  drawable 	android.R  ic_dialog_info android.R.drawable  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  	Alignment android.app.Activity  Arrangement android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  Column android.app.Activity  
Composable android.app.Activity  FocusFlowApp android.app.Activity  FocusFlowTheme android.app.Activity  
FontWeight android.app.Activity  Intent android.app.Activity  LocalContext android.app.Activity  MainActivity android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  OnboardingScreen android.app.Activity  Spacer android.app.Activity  Surface android.app.Activity  Text android.app.Activity  	TextAlign android.app.Activity  dp android.app.Activity  fillMaxSize android.app.Activity  fillMaxWidth android.app.Activity  finish android.app.Activity  height android.app.Activity  java android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  sp android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  IMPORTANCE_DEFAULT android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  MainActivity android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  START_NOT_STICKY android.app.Service  String android.app.Service  android android.app.Service  apply android.app.Service  createNotificationChannel android.app.Service  getSystemService android.app.Service  java android.app.Service  onCreate android.app.Service  showNotification android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  NotificationService !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  	Alignment android.content.Context  Arrangement android.content.Context  Build android.content.Context  Bundle android.content.Context  Button android.content.Context  
CHANNEL_ID android.content.Context  Column android.content.Context  
Composable android.content.Context  Context android.content.Context  FocusFlowApp android.content.Context  FocusFlowTheme android.content.Context  
FontWeight android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  LocalContext android.content.Context  MainActivity android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  OnboardingScreen android.content.Context  
PendingIntent android.content.Context  START_NOT_STICKY android.content.Context  Spacer android.content.Context  String android.content.Context  Surface android.content.Context  Text android.content.Context  	TextAlign android.content.Context  android android.content.Context  applicationContext android.content.Context  apply android.content.Context  createNotificationChannel android.content.Context  dp android.content.Context  fillMaxSize android.content.Context  fillMaxWidth android.content.Context  finish android.content.Context  getAPPLICATIONContext android.content.Context  getApplicationContext android.content.Context  getSystemService android.content.Context  height android.content.Context  java android.content.Context  onCreate android.content.Context  padding android.content.Context  setApplicationContext android.content.Context  
setContent android.content.Context  showNotification android.content.Context  sp android.content.Context  
startActivity android.content.Context  startService android.content.Context  	Alignment android.content.ContextWrapper  Arrangement android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Column android.content.ContextWrapper  
Composable android.content.ContextWrapper  Context android.content.ContextWrapper  FocusFlowApp android.content.ContextWrapper  FocusFlowTheme android.content.ContextWrapper  
FontWeight android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  LocalContext android.content.ContextWrapper  MainActivity android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  OnboardingScreen android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  Spacer android.content.ContextWrapper  String android.content.ContextWrapper  Surface android.content.ContextWrapper  Text android.content.ContextWrapper  	TextAlign android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  dp android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  fillMaxWidth android.content.ContextWrapper  finish android.content.ContextWrapper  getSystemService android.content.ContextWrapper  height android.content.ContextWrapper  java android.content.ContextWrapper  onCreate android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  showNotification android.content.ContextWrapper  sp android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  apply android.content.Intent  flags android.content.Intent  getAPPLY android.content.Intent  getApply android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  setFlags android.content.Intent  Build 
android.os  Bundle 
android.os  IBinder 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  Log android.util  Patterns android.util  e android.util.Log  i android.util.Log  w android.util.Log  
EMAIL_ADDRESS android.util.Patterns  	Alignment  android.view.ContextThemeWrapper  Arrangement  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  Column  android.view.ContextThemeWrapper  
Composable  android.view.ContextThemeWrapper  FocusFlowApp  android.view.ContextThemeWrapper  FocusFlowTheme  android.view.ContextThemeWrapper  
FontWeight  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LocalContext  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  OnboardingScreen  android.view.ContextThemeWrapper  Spacer  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  	TextAlign  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  fillMaxWidth  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  height  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  sp  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  	Alignment #androidx.activity.ComponentActivity  Arrangement #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  Column #androidx.activity.ComponentActivity  
Composable #androidx.activity.ComponentActivity  FocusFlowApp #androidx.activity.ComponentActivity  FocusFlowTheme #androidx.activity.ComponentActivity  
FontWeight #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LocalContext #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  OnboardingScreen #androidx.activity.ComponentActivity  Spacer #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  	TextAlign #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  fillMaxWidth #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  height #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  sp #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  
AICoachScreen /androidx.compose.animation.AnimatedContentScope  BudgetScreen /androidx.compose.animation.AnimatedContentScope  DashboardScreen /androidx.compose.animation.AnimatedContentScope  
DebtScreen /androidx.compose.animation.AnimatedContentScope  ExpensesScreen /androidx.compose.animation.AnimatedContentScope  HabitsScreen /androidx.compose.animation.AnimatedContentScope  TasksScreen /androidx.compose.animation.AnimatedContentScope  Add androidx.compose.animation.core  AlertDialog androidx.compose.animation.core  	Alignment androidx.compose.animation.core  Arrangement androidx.compose.animation.core  Box androidx.compose.animation.core  Card androidx.compose.animation.core  CircleShape androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  CompositionLocalProvider androidx.compose.animation.core  Delete androidx.compose.animation.core  ExpenseCategories androidx.compose.animation.core  ExpenseItem androidx.compose.animation.core  
FilterChip androidx.compose.animation.core  FloatingActionButton androidx.compose.animation.core  
FontWeight androidx.compose.animation.core  	GridCells androidx.compose.animation.core  Icon androidx.compose.animation.core  
IconButton androidx.compose.animation.core  Icons androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  KeyboardOptions androidx.compose.animation.core  KeyboardType androidx.compose.animation.core  LaunchedEffect androidx.compose.animation.core  
LazyColumn androidx.compose.animation.core  LazyRow androidx.compose.animation.core  LazyVerticalGrid androidx.compose.animation.core  LocalContentColor androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  
MessageBubble androidx.compose.animation.core  MessageInputField androidx.compose.animation.core  Modifier androidx.compose.animation.core  OutlinedTextField androidx.compose.animation.core  Person androidx.compose.animation.core  QuickCategoryOverview androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  Row androidx.compose.animation.core  Send androidx.compose.animation.core  Spacer androidx.compose.animation.core  SpendingSummaryCard androidx.compose.animation.core  String androidx.compose.animation.core  SuggestedPromptCard androidx.compose.animation.core  SuggestedPromptsSection androidx.compose.animation.core  Surface androidx.compose.animation.core  System androidx.compose.animation.core  Text androidx.compose.animation.core  
TextButton androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  TypingIndicator androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  
background androidx.compose.animation.core  
component1 androidx.compose.animation.core  
component2 androidx.compose.animation.core  	emptyList androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  forEach androidx.compose.animation.core  format androidx.compose.animation.core  
formatDate androidx.compose.animation.core  getValue androidx.compose.animation.core  groupBy androidx.compose.animation.core  height androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  
isNotBlank androidx.compose.animation.core  
isNotEmpty androidx.compose.animation.core  items androidx.compose.animation.core  let androidx.compose.animation.core  listOf androidx.compose.animation.core  	lowercase androidx.compose.animation.core  	mapValues androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  padding androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  repeat androidx.compose.animation.core  replaceFirstChar androidx.compose.animation.core  setValue androidx.compose.animation.core  size androidx.compose.animation.core  sortedByDescending androidx.compose.animation.core  sumOf androidx.compose.animation.core  take androidx.compose.animation.core  takeIf androidx.compose.animation.core  toDoubleOrNull androidx.compose.animation.core  toList androidx.compose.animation.core  tween androidx.compose.animation.core  width androidx.compose.animation.core  widthIn androidx.compose.animation.core  Reverse *androidx.compose.animation.core.RepeatMode  Image androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ADHDFriendlyStep "androidx.compose.foundation.layout  
AICoachScreen "androidx.compose.foundation.layout  
AccountCircle "androidx.compose.foundation.layout  AchievementBadge "androidx.compose.foundation.layout  Add "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  BottomNavigation "androidx.compose.foundation.layout  BottomNavigationItem "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  BudgetAmountItem "androidx.compose.foundation.layout  BudgetCategoryItem "androidx.compose.foundation.layout  BudgetOverviewCard "androidx.compose.foundation.layout  BudgetScreen "androidx.compose.foundation.layout  BudgetSetupStep "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CheckCircle "androidx.compose.foundation.layout  Checkbox "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  CompleteStep "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  CompositionLocalProvider "androidx.compose.foundation.layout  CreditCardItem "androidx.compose.foundation.layout  CreditCardSummaryCard "androidx.compose.foundation.layout  DashboardScreen "androidx.compose.foundation.layout  DebtOverviewCard "androidx.compose.foundation.layout  
DebtScreen "androidx.compose.foundation.layout  
DebtSetupStep "androidx.compose.foundation.layout  Delete "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  EmptyBudgetState "androidx.compose.foundation.layout  EmptyStateCard "androidx.compose.foundation.layout  ExpenseCategories "androidx.compose.foundation.layout  ExpenseItem "androidx.compose.foundation.layout  ExpensesScreen "androidx.compose.foundation.layout  Face "androidx.compose.foundation.layout  FavoriteBorder "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  FinancialGoalsStep "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  FocusFlowApp "androidx.compose.foundation.layout  FocusFlowTheme "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  HabitStreakCard "androidx.compose.foundation.layout  HabitStreakItem "androidx.compose.foundation.layout  HabitsScreen "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  ImpulseControlQuestions "androidx.compose.foundation.layout  IncomeSetupStep "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  KeyboardArrowRight "androidx.compose.foundation.layout  KeyboardArrowUp "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  LinearProgressIndicator "androidx.compose.foundation.layout  LocalContentColor "androidx.compose.foundation.layout  LocalContext "androidx.compose.foundation.layout  MainActivity "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  
MessageBubble "androidx.compose.foundation.layout  MessageInputField "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MotivationalQuoteCard "androidx.compose.foundation.layout  NotificationSetupStep "androidx.compose.foundation.layout  
Notifications "androidx.compose.foundation.layout  OnboardingProgressIndicator "androidx.compose.foundation.layout  OnboardingStep "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  Pair "androidx.compose.foundation.layout  PayoffStrategy "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  PersonalGoalsStep "androidx.compose.foundation.layout  ProgressAchievementsCard "androidx.compose.foundation.layout  QuickCategoryOverview "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SafeToSpendWidget "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  Send "androidx.compose.foundation.layout  ShoppingCart "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  SpendingSummaryCard "androidx.compose.foundation.layout  Star "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SuggestedPromptCard "androidx.compose.foundation.layout  SuggestedPromptsSection "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  TaskItem "androidx.compose.foundation.layout  TasksScreen "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TodaysTasksCard "androidx.compose.foundation.layout  TypingIndicator "androidx.compose.foundation.layout  VirtualPetWidget "androidx.compose.foundation.layout  Warning "androidx.compose.foundation.layout  WelcomeStep "androidx.compose.foundation.layout  animateFloatAsState "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  bottomNavItems "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  coerceIn "androidx.compose.foundation.layout  
component1 "androidx.compose.foundation.layout  
component2 "androidx.compose.foundation.layout  
composable "androidx.compose.foundation.layout  currentBackStackEntryAsState "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  finish "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  
formatDate "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  groupBy "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  	lowercase "androidx.compose.foundation.layout  	mapValues "androidx.compose.foundation.layout  minus "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  repeat "androidx.compose.foundation.layout  replaceFirstChar "androidx.compose.foundation.layout  set "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  sortedByDescending "androidx.compose.foundation.layout  sumOf "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  takeIf "androidx.compose.foundation.layout  toDoubleOrNull "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  toList "androidx.compose.foundation.layout  toMap "androidx.compose.foundation.layout  toMutableMap "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  widthIn "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  End .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Add +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  CompositionLocalProvider +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  LocalContentColor +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  ADHDFriendlyStep .androidx.compose.foundation.layout.ColumnScope  AchievementBadge .androidx.compose.foundation.layout.ColumnScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  BudgetAmountItem .androidx.compose.foundation.layout.ColumnScope  BudgetCategoryItem .androidx.compose.foundation.layout.ColumnScope  BudgetOverviewCard .androidx.compose.foundation.layout.ColumnScope  BudgetSetupStep .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CheckCircle .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  CompleteStep .androidx.compose.foundation.layout.ColumnScope  CreditCardItem .androidx.compose.foundation.layout.ColumnScope  DebtOverviewCard .androidx.compose.foundation.layout.ColumnScope  
DebtSetupStep .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  EmptyBudgetState .androidx.compose.foundation.layout.ColumnScope  EmptyStateCard .androidx.compose.foundation.layout.ColumnScope  ExpenseCategories .androidx.compose.foundation.layout.ColumnScope  ExpenseItem .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  FinancialGoalsStep .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  HabitStreakItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  ImpulseControlQuestions .androidx.compose.foundation.layout.ColumnScope  IncomeSetupStep .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowRight .androidx.compose.foundation.layout.ColumnScope  KeyboardArrowUp .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  LinearProgressIndicator .androidx.compose.foundation.layout.ColumnScope  MainActivity .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  
MessageBubble .androidx.compose.foundation.layout.ColumnScope  MessageInputField .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  NotificationSetupStep .androidx.compose.foundation.layout.ColumnScope  OnboardingProgressIndicator .androidx.compose.foundation.layout.ColumnScope  OnboardingStep .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PayoffStrategy .androidx.compose.foundation.layout.ColumnScope  PersonalGoalsStep .androidx.compose.foundation.layout.ColumnScope  QuickCategoryOverview .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  ShoppingCart .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  SpendingSummaryCard .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  SuggestedPromptCard .androidx.compose.foundation.layout.ColumnScope  SuggestedPromptsSection .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  TaskItem .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TypingIndicator .androidx.compose.foundation.layout.ColumnScope  Warning .androidx.compose.foundation.layout.ColumnScope  WelcomeStep .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  coerceIn .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  finish .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
formatDate .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  getCLICKABLE .androidx.compose.foundation.layout.ColumnScope  getCOERCEIn .androidx.compose.foundation.layout.ColumnScope  getClickable .androidx.compose.foundation.layout.ColumnScope  getCoerceIn .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFINISH .androidx.compose.foundation.layout.ColumnScope  	getFORMAT .androidx.compose.foundation.layout.ColumnScope  
getFORMATDate .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFinish .androidx.compose.foundation.layout.ColumnScope  	getFormat .androidx.compose.foundation.layout.ColumnScope  
getFormatDate .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISNotBlank .androidx.compose.foundation.layout.ColumnScope  
getIsNotBlank .androidx.compose.foundation.layout.ColumnScope  	getLAUNCH .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  getLOWERCASE .androidx.compose.foundation.layout.ColumnScope  	getLaunch .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  getLowercase .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getREPLACEFirstChar .androidx.compose.foundation.layout.ColumnScope  getReplaceFirstChar .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getTAKE .androidx.compose.foundation.layout.ColumnScope  getTODoubleOrNull .androidx.compose.foundation.layout.ColumnScope  getTake .androidx.compose.foundation.layout.ColumnScope  getToDoubleOrNull .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  	lowercase .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  replaceFirstChar .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  toDoubleOrNull .androidx.compose.foundation.layout.ColumnScope  viewModelScope .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  AchievementBadge +androidx.compose.foundation.layout.RowScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  BottomNavigationItem +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  BudgetAmountItem +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CheckCircle +androidx.compose.foundation.layout.RowScope  Checkbox +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  DebtOverviewCard +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  
FilterChip +androidx.compose.foundation.layout.RowScope  FloatingActionButton +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  HabitStreakItem +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  KeyboardArrowRight +androidx.compose.foundation.layout.RowScope  KeyboardArrowUp +androidx.compose.foundation.layout.RowScope  LinearProgressIndicator +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  PayoffStrategy +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  
RepeatMode +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Send +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  System +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Warning +androidx.compose.foundation.layout.RowScope  animateFloatAsState +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  bottomNavItems +androidx.compose.foundation.layout.RowScope  currentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  
formatDate +androidx.compose.foundation.layout.RowScope  getANIMATEFloatAsState +androidx.compose.foundation.layout.RowScope  getAnimateFloatAsState +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  getBOTTOMNavItems +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getBottomNavItems +androidx.compose.foundation.layout.RowScope  getCURRENTBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  getCurrentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  getFILLMaxWidth +androidx.compose.foundation.layout.RowScope  	getFORMAT +androidx.compose.foundation.layout.RowScope  
getFORMATDate +androidx.compose.foundation.layout.RowScope  getFillMaxWidth +androidx.compose.foundation.layout.RowScope  	getFormat +androidx.compose.foundation.layout.RowScope  
getFormatDate +androidx.compose.foundation.layout.RowScope  getGETValue +androidx.compose.foundation.layout.RowScope  getGetValue +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  getINFINITERepeatable +androidx.compose.foundation.layout.RowScope  
getISNotBlank +androidx.compose.foundation.layout.RowScope  getInfiniteRepeatable +androidx.compose.foundation.layout.RowScope  
getIsNotBlank +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  	getLISTOf +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  	getListOf +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  getPROVIDEDelegate +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getProvideDelegate +androidx.compose.foundation.layout.RowScope  	getREPEAT +androidx.compose.foundation.layout.RowScope  	getRepeat +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getTWEEN +androidx.compose.foundation.layout.RowScope  getTween +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  
getWIDTHIn +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  
getWidthIn +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  infiniteRepeatable +androidx.compose.foundation.layout.RowScope  invoke +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  repeat +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  tween +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  widthIn +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  BudgetCategoryItem .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  Checkbox .androidx.compose.foundation.lazy.LazyItemScope  CreditCardItem .androidx.compose.foundation.lazy.LazyItemScope  CreditCardSummaryCard .androidx.compose.foundation.lazy.LazyItemScope  ExpenseItem .androidx.compose.foundation.lazy.LazyItemScope  
FilterChip .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  HabitStreakCard .androidx.compose.foundation.lazy.LazyItemScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyItemScope  KeyboardType .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  
MessageBubble .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  MotivationalQuoteCard .androidx.compose.foundation.lazy.LazyItemScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyItemScope  ProgressAchievementsCard .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  SafeToSpendWidget .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TodaysTasksCard .androidx.compose.foundation.lazy.LazyItemScope  TypingIndicator .androidx.compose.foundation.lazy.LazyItemScope  VirtualPetWidget .androidx.compose.foundation.lazy.LazyItemScope  	clickable .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getCLICKABLE .androidx.compose.foundation.lazy.LazyItemScope  getClickable .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getGETValue .androidx.compose.foundation.lazy.LazyItemScope  getGetValue .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  getLET .androidx.compose.foundation.lazy.LazyItemScope  getLet .androidx.compose.foundation.lazy.LazyItemScope  getMINUS .androidx.compose.foundation.lazy.LazyItemScope  getMUTABLEStateOf .androidx.compose.foundation.lazy.LazyItemScope  getMinus .androidx.compose.foundation.lazy.LazyItemScope  getMutableStateOf .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  getPLUS .androidx.compose.foundation.lazy.LazyItemScope  getPROVIDEDelegate .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  getPlus .androidx.compose.foundation.lazy.LazyItemScope  getProvideDelegate .androidx.compose.foundation.lazy.LazyItemScope  getREMEMBER .androidx.compose.foundation.lazy.LazyItemScope  getRemember .androidx.compose.foundation.lazy.LazyItemScope  getSET .androidx.compose.foundation.lazy.LazyItemScope  getSETValue .androidx.compose.foundation.lazy.LazyItemScope  getSet .androidx.compose.foundation.lazy.LazyItemScope  getSetValue .androidx.compose.foundation.lazy.LazyItemScope  getTODoubleOrNull .androidx.compose.foundation.lazy.LazyItemScope  getToDoubleOrNull .androidx.compose.foundation.lazy.LazyItemScope  getValue .androidx.compose.foundation.lazy.LazyItemScope  getWIDTH .androidx.compose.foundation.lazy.LazyItemScope  getWidth .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  invoke .androidx.compose.foundation.lazy.LazyItemScope  let .androidx.compose.foundation.lazy.LazyItemScope  minus .androidx.compose.foundation.lazy.LazyItemScope  mutableStateOf .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  plus .androidx.compose.foundation.lazy.LazyItemScope  provideDelegate .androidx.compose.foundation.lazy.LazyItemScope  remember .androidx.compose.foundation.lazy.LazyItemScope  set .androidx.compose.foundation.lazy.LazyItemScope  setValue .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  BudgetCategoryItem .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  Checkbox .androidx.compose.foundation.lazy.LazyListScope  CreditCardItem .androidx.compose.foundation.lazy.LazyListScope  CreditCardSummaryCard .androidx.compose.foundation.lazy.LazyListScope  ExpenseCategories .androidx.compose.foundation.lazy.LazyListScope  ExpenseItem .androidx.compose.foundation.lazy.LazyListScope  
FilterChip .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  HabitStreakCard .androidx.compose.foundation.lazy.LazyListScope  KeyboardOptions .androidx.compose.foundation.lazy.LazyListScope  KeyboardType .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  
MessageBubble .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  MotivationalQuoteCard .androidx.compose.foundation.lazy.LazyListScope  OutlinedTextField .androidx.compose.foundation.lazy.LazyListScope  ProgressAchievementsCard .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  SafeToSpendWidget .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TodaysTasksCard .androidx.compose.foundation.lazy.LazyListScope  TypingIndicator .androidx.compose.foundation.lazy.LazyListScope  VirtualPetWidget .androidx.compose.foundation.lazy.LazyListScope  	clickable .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getCLICKABLE .androidx.compose.foundation.lazy.LazyListScope  getClickable .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getGETValue .androidx.compose.foundation.lazy.LazyListScope  getGetValue .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  getLET .androidx.compose.foundation.lazy.LazyListScope  getLet .androidx.compose.foundation.lazy.LazyListScope  getMINUS .androidx.compose.foundation.lazy.LazyListScope  getMUTABLEStateOf .androidx.compose.foundation.lazy.LazyListScope  getMinus .androidx.compose.foundation.lazy.LazyListScope  getMutableStateOf .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  getPLUS .androidx.compose.foundation.lazy.LazyListScope  getPROVIDEDelegate .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  getPlus .androidx.compose.foundation.lazy.LazyListScope  getProvideDelegate .androidx.compose.foundation.lazy.LazyListScope  getREMEMBER .androidx.compose.foundation.lazy.LazyListScope  getRemember .androidx.compose.foundation.lazy.LazyListScope  getSET .androidx.compose.foundation.lazy.LazyListScope  getSETValue .androidx.compose.foundation.lazy.LazyListScope  getSet .androidx.compose.foundation.lazy.LazyListScope  getSetValue .androidx.compose.foundation.lazy.LazyListScope  getTAKE .androidx.compose.foundation.lazy.LazyListScope  getTODoubleOrNull .androidx.compose.foundation.lazy.LazyListScope  	getTOList .androidx.compose.foundation.lazy.LazyListScope  getTake .androidx.compose.foundation.lazy.LazyListScope  getToDoubleOrNull .androidx.compose.foundation.lazy.LazyListScope  	getToList .androidx.compose.foundation.lazy.LazyListScope  getValue .androidx.compose.foundation.lazy.LazyListScope  getWIDTH .androidx.compose.foundation.lazy.LazyListScope  getWidth .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  invoke .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  let .androidx.compose.foundation.lazy.LazyListScope  minus .androidx.compose.foundation.lazy.LazyListScope  mutableStateOf .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  plus .androidx.compose.foundation.lazy.LazyListScope  provideDelegate .androidx.compose.foundation.lazy.LazyListScope  remember .androidx.compose.foundation.lazy.LazyListScope  set .androidx.compose.foundation.lazy.LazyListScope  setValue .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  take .androidx.compose.foundation.lazy.LazyListScope  toDoubleOrNull .androidx.compose.foundation.lazy.LazyListScope  toList .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Fixed /androidx.compose.foundation.lazy.grid.GridCells  SuggestedPromptCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  SuggestedPromptCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  getITEMS 3androidx.compose.foundation.lazy.grid.LazyGridScope  getItems 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  invoke :androidx.compose.foundation.text.KeyboardOptions.Companion  ADHDFriendlyStep androidx.compose.material  
AICoachScreen androidx.compose.material  
AccountCircle androidx.compose.material  AchievementBadge androidx.compose.material  Add androidx.compose.material  AlertDialog androidx.compose.material  	Alignment androidx.compose.material  Arrangement androidx.compose.material  BottomNavigation androidx.compose.material  BottomNavigationItem androidx.compose.material  Box androidx.compose.material  BudgetAmountItem androidx.compose.material  BudgetCategoryItem androidx.compose.material  BudgetOverviewCard androidx.compose.material  BudgetScreen androidx.compose.material  BudgetSetupStep androidx.compose.material  Button androidx.compose.material  ButtonColors androidx.compose.material  ButtonDefaults androidx.compose.material  Card androidx.compose.material  CheckCircle androidx.compose.material  Checkbox androidx.compose.material  CircleShape androidx.compose.material  CircularProgressIndicator androidx.compose.material  Color androidx.compose.material  Colors androidx.compose.material  Column androidx.compose.material  CompleteStep androidx.compose.material  
Composable androidx.compose.material  CompositionLocalProvider androidx.compose.material  CreditCardItem androidx.compose.material  CreditCardSummaryCard androidx.compose.material  DashboardScreen androidx.compose.material  DebtOverviewCard androidx.compose.material  
DebtScreen androidx.compose.material  
DebtSetupStep androidx.compose.material  Delete androidx.compose.material  Edit androidx.compose.material  EmptyBudgetState androidx.compose.material  EmptyStateCard androidx.compose.material  ExpenseCategories androidx.compose.material  ExpenseItem androidx.compose.material  ExpensesScreen androidx.compose.material  Face androidx.compose.material  FavoriteBorder androidx.compose.material  
FilterChip androidx.compose.material  FinancialGoalsStep androidx.compose.material  FloatingActionButton androidx.compose.material  FocusFlowApp androidx.compose.material  FocusFlowTheme androidx.compose.material  
FontWeight androidx.compose.material  	GridCells androidx.compose.material  HabitStreakCard androidx.compose.material  HabitStreakItem androidx.compose.material  HabitsScreen androidx.compose.material  Icon androidx.compose.material  
IconButton androidx.compose.material  Icons androidx.compose.material  ImpulseControlQuestions androidx.compose.material  IncomeSetupStep androidx.compose.material  Info androidx.compose.material  Intent androidx.compose.material  KeyboardArrowRight androidx.compose.material  KeyboardArrowUp androidx.compose.material  KeyboardOptions androidx.compose.material  KeyboardType androidx.compose.material  LaunchedEffect androidx.compose.material  
LazyColumn androidx.compose.material  LazyRow androidx.compose.material  LazyVerticalGrid androidx.compose.material  LinearProgressIndicator androidx.compose.material  LocalContentColor androidx.compose.material  LocalContext androidx.compose.material  MainActivity androidx.compose.material  
MaterialTheme androidx.compose.material  
MessageBubble androidx.compose.material  MessageInputField androidx.compose.material  Modifier androidx.compose.material  MotivationalQuoteCard androidx.compose.material  NotificationSetupStep androidx.compose.material  
Notifications androidx.compose.material  OnboardingProgressIndicator androidx.compose.material  OnboardingStep androidx.compose.material  OutlinedButton androidx.compose.material  OutlinedTextField androidx.compose.material  Pair androidx.compose.material  PayoffStrategy androidx.compose.material  Person androidx.compose.material  PersonalGoalsStep androidx.compose.material  ProgressAchievementsCard androidx.compose.material  QuickCategoryOverview androidx.compose.material  RadioButton androidx.compose.material  
RepeatMode androidx.compose.material  RoundedCornerShape androidx.compose.material  Row androidx.compose.material  SafeToSpendWidget androidx.compose.material  Scaffold androidx.compose.material  Screen androidx.compose.material  Send androidx.compose.material  Shapes androidx.compose.material  ShoppingCart androidx.compose.material  SnackbarDuration androidx.compose.material  SnackbarHostState androidx.compose.material  SnackbarResult androidx.compose.material  Spacer androidx.compose.material  SpendingSummaryCard androidx.compose.material  Star androidx.compose.material  String androidx.compose.material  SuggestedPromptCard androidx.compose.material  SuggestedPromptsSection androidx.compose.material  Surface androidx.compose.material  Switch androidx.compose.material  System androidx.compose.material  TaskItem androidx.compose.material  TasksScreen androidx.compose.material  Text androidx.compose.material  	TextAlign androidx.compose.material  
TextButton androidx.compose.material  TodaysTasksCard androidx.compose.material  TypingIndicator androidx.compose.material  
Typography androidx.compose.material  VirtualPetWidget androidx.compose.material  Warning androidx.compose.material  WelcomeStep androidx.compose.material  animateFloatAsState androidx.compose.material  
background androidx.compose.material  bottomNavItems androidx.compose.material  	clickable androidx.compose.material  coerceIn androidx.compose.material  
component1 androidx.compose.material  
component2 androidx.compose.material  
composable androidx.compose.material  currentBackStackEntryAsState androidx.compose.material  
darkColors androidx.compose.material  delay androidx.compose.material  	emptyList androidx.compose.material  fillMaxSize androidx.compose.material  fillMaxWidth androidx.compose.material  finish androidx.compose.material  forEach androidx.compose.material  format androidx.compose.material  
formatDate androidx.compose.material  getValue androidx.compose.material  groupBy androidx.compose.material  height androidx.compose.material  infiniteRepeatable androidx.compose.material  
isNotBlank androidx.compose.material  
isNotEmpty androidx.compose.material  items androidx.compose.material  java androidx.compose.material  launch androidx.compose.material  let androidx.compose.material  lightColors androidx.compose.material  listOf androidx.compose.material  	lowercase androidx.compose.material  	mapValues androidx.compose.material  minus androidx.compose.material  mutableStateOf androidx.compose.material  padding androidx.compose.material  plus androidx.compose.material  provideDelegate androidx.compose.material  remember androidx.compose.material  repeat androidx.compose.material  replaceFirstChar androidx.compose.material  set androidx.compose.material  
setContent androidx.compose.material  setValue androidx.compose.material  size androidx.compose.material  sortedByDescending androidx.compose.material  sumOf androidx.compose.material  take androidx.compose.material  takeIf androidx.compose.material  toDoubleOrNull androidx.compose.material  toIntOrNull androidx.compose.material  toList androidx.compose.material  toMap androidx.compose.material  toMutableMap androidx.compose.material  tween androidx.compose.material  width androidx.compose.material  widthIn androidx.compose.material  buttonColors (androidx.compose.material.ButtonDefaults  
background  androidx.compose.material.Colors  error  androidx.compose.material.Colors  	onPrimary  androidx.compose.material.Colors  	onSurface  androidx.compose.material.Colors  primary  androidx.compose.material.Colors  	secondary  androidx.compose.material.Colors  surface  androidx.compose.material.Colors  colors 'androidx.compose.material.MaterialTheme  invoke 'androidx.compose.material.MaterialTheme  
typography 'androidx.compose.material.MaterialTheme  Short *androidx.compose.material.SnackbarDuration  showSnackbar +androidx.compose.material.SnackbarHostState  body1 $androidx.compose.material.Typography  body2 $androidx.compose.material.Typography  button $androidx.compose.material.Typography  caption $androidx.compose.material.Typography  h3 $androidx.compose.material.Typography  h4 $androidx.compose.material.Typography  h5 $androidx.compose.material.Typography  h6 $androidx.compose.material.Typography  	subtitle1 $androidx.compose.material.Typography  	subtitle2 $androidx.compose.material.Typography  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  
AccountCircle ,androidx.compose.material.icons.Icons.Filled  Add ,androidx.compose.material.icons.Icons.Filled  CheckCircle ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Face ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  FavoriteBorder ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowRight ,androidx.compose.material.icons.Icons.Filled  KeyboardArrowUp ,androidx.compose.material.icons.Icons.Filled  List ,androidx.compose.material.icons.Icons.Filled  
Notifications ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Send ,androidx.compose.material.icons.Icons.Filled  ShoppingCart ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Warning ,androidx.compose.material.icons.Icons.Filled  ADHDFriendlyStep &androidx.compose.material.icons.filled  
AccountCircle &androidx.compose.material.icons.filled  Add &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  BudgetAmountItem &androidx.compose.material.icons.filled  BudgetCategoryItem &androidx.compose.material.icons.filled  BudgetOverviewCard &androidx.compose.material.icons.filled  BudgetSetupStep &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Checkbox &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  CompleteStep &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  CompositionLocalProvider &androidx.compose.material.icons.filled  CreditCardItem &androidx.compose.material.icons.filled  DebtOverviewCard &androidx.compose.material.icons.filled  
DebtSetupStep &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  EmptyBudgetState &androidx.compose.material.icons.filled  EmptyStateCard &androidx.compose.material.icons.filled  ExpenseCategories &androidx.compose.material.icons.filled  ExpenseItem &androidx.compose.material.icons.filled  Face &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  
FilterChip &androidx.compose.material.icons.filled  FinancialGoalsStep &androidx.compose.material.icons.filled  FloatingActionButton &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  	GridCells &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  ImpulseControlQuestions &androidx.compose.material.icons.filled  IncomeSetupStep &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  KeyboardArrowRight &androidx.compose.material.icons.filled  KeyboardArrowUp &androidx.compose.material.icons.filled  KeyboardOptions &androidx.compose.material.icons.filled  KeyboardType &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  LazyVerticalGrid &androidx.compose.material.icons.filled  LinearProgressIndicator &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  LocalContentColor &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  
MessageBubble &androidx.compose.material.icons.filled  MessageInputField &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  NotificationSetupStep &androidx.compose.material.icons.filled  
Notifications &androidx.compose.material.icons.filled  OnboardingProgressIndicator &androidx.compose.material.icons.filled  OnboardingStep &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  Pair &androidx.compose.material.icons.filled  PayoffStrategy &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  PersonalGoalsStep &androidx.compose.material.icons.filled  QuickCategoryOverview &androidx.compose.material.icons.filled  RadioButton &androidx.compose.material.icons.filled  
RepeatMode &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Send &androidx.compose.material.icons.filled  ShoppingCart &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  SpendingSummaryCard &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  SuggestedPromptCard &androidx.compose.material.icons.filled  SuggestedPromptsSection &androidx.compose.material.icons.filled  Surface &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  System &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TypingIndicator &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  WelcomeStep &androidx.compose.material.icons.filled  animateFloatAsState &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  coerceIn &androidx.compose.material.icons.filled  
component1 &androidx.compose.material.icons.filled  
component2 &androidx.compose.material.icons.filled  delay &androidx.compose.material.icons.filled  	emptyList &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  forEach &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  
formatDate &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  groupBy &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  infiniteRepeatable &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  	lowercase &androidx.compose.material.icons.filled  	mapValues &androidx.compose.material.icons.filled  minus &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  plus &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  repeat &androidx.compose.material.icons.filled  replaceFirstChar &androidx.compose.material.icons.filled  set &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  sortedByDescending &androidx.compose.material.icons.filled  sumOf &androidx.compose.material.icons.filled  take &androidx.compose.material.icons.filled  takeIf &androidx.compose.material.icons.filled  toDoubleOrNull &androidx.compose.material.icons.filled  toIntOrNull &androidx.compose.material.icons.filled  toList &androidx.compose.material.icons.filled  toMap &androidx.compose.material.icons.filled  toMutableMap &androidx.compose.material.icons.filled  tween &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  widthIn &androidx.compose.material.icons.filled  ADHDFriendlyStep androidx.compose.runtime  
AICoachScreen androidx.compose.runtime  
AccountCircle androidx.compose.runtime  AchievementBadge androidx.compose.runtime  Add androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  BudgetAmountItem androidx.compose.runtime  BudgetCategoryItem androidx.compose.runtime  BudgetOverviewCard androidx.compose.runtime  BudgetScreen androidx.compose.runtime  BudgetSetupStep androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CheckCircle androidx.compose.runtime  Checkbox androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  CompleteStep androidx.compose.runtime  
Composable androidx.compose.runtime  CompositionLocalProvider androidx.compose.runtime  CreditCardItem androidx.compose.runtime  CreditCardSummaryCard androidx.compose.runtime  DashboardScreen androidx.compose.runtime  DebtOverviewCard androidx.compose.runtime  
DebtScreen androidx.compose.runtime  
DebtSetupStep androidx.compose.runtime  Delete androidx.compose.runtime  Edit androidx.compose.runtime  EmptyBudgetState androidx.compose.runtime  EmptyStateCard androidx.compose.runtime  ExpenseCategories androidx.compose.runtime  ExpenseItem androidx.compose.runtime  ExpensesScreen androidx.compose.runtime  Face androidx.compose.runtime  FavoriteBorder androidx.compose.runtime  
FilterChip androidx.compose.runtime  FinancialGoalsStep androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  FocusFlowApp androidx.compose.runtime  FocusFlowTheme androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  HabitStreakCard androidx.compose.runtime  HabitStreakItem androidx.compose.runtime  HabitsScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  ImpulseControlQuestions androidx.compose.runtime  IncomeSetupStep androidx.compose.runtime  Info androidx.compose.runtime  Intent androidx.compose.runtime  KeyboardArrowRight androidx.compose.runtime  KeyboardArrowUp androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  LinearProgressIndicator androidx.compose.runtime  LocalContentColor androidx.compose.runtime  LocalContext androidx.compose.runtime  MainActivity androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  
MessageBubble androidx.compose.runtime  MessageInputField androidx.compose.runtime  Modifier androidx.compose.runtime  MotivationalQuoteCard androidx.compose.runtime  MutableState androidx.compose.runtime  NotificationSetupStep androidx.compose.runtime  
Notifications androidx.compose.runtime  OnboardingProgressIndicator androidx.compose.runtime  OnboardingStep androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  Pair androidx.compose.runtime  PayoffStrategy androidx.compose.runtime  Person androidx.compose.runtime  PersonalGoalsStep androidx.compose.runtime  ProgressAchievementsCard androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
ProvidedValue androidx.compose.runtime  QuickCategoryOverview androidx.compose.runtime  RadioButton androidx.compose.runtime  
RepeatMode androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  SafeToSpendWidget androidx.compose.runtime  Scaffold androidx.compose.runtime  Screen androidx.compose.runtime  Send androidx.compose.runtime  ShoppingCart androidx.compose.runtime  Spacer androidx.compose.runtime  SpendingSummaryCard androidx.compose.runtime  Star androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SuggestedPromptCard androidx.compose.runtime  SuggestedPromptsSection androidx.compose.runtime  Surface androidx.compose.runtime  Switch androidx.compose.runtime  System androidx.compose.runtime  TaskItem androidx.compose.runtime  TasksScreen androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  TodaysTasksCard androidx.compose.runtime  TypingIndicator androidx.compose.runtime  VirtualPetWidget androidx.compose.runtime  Warning androidx.compose.runtime  WelcomeStep androidx.compose.runtime  animateFloatAsState androidx.compose.runtime  
background androidx.compose.runtime  	clickable androidx.compose.runtime  coerceIn androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  
composable androidx.compose.runtime  delay androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  finish androidx.compose.runtime  forEach androidx.compose.runtime  format androidx.compose.runtime  
formatDate androidx.compose.runtime  getValue androidx.compose.runtime  groupBy androidx.compose.runtime  height androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  	lowercase androidx.compose.runtime  	mapValues androidx.compose.runtime  minus androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  repeat androidx.compose.runtime  replaceFirstChar androidx.compose.runtime  set androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  sortedByDescending androidx.compose.runtime  sumOf androidx.compose.runtime  take androidx.compose.runtime  takeIf androidx.compose.runtime  toDoubleOrNull androidx.compose.runtime  toIntOrNull androidx.compose.runtime  toList androidx.compose.runtime  toMap androidx.compose.runtime  toMutableMap androidx.compose.runtime  tween androidx.compose.runtime  width androidx.compose.runtime  widthIn androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  provides )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  provides 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Bottom androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  End androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Bottom 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  End 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  widthIn androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  
getWIDTHIn &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  
getWidthIn &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  widthIn &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  equals "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  Decimal +androidx.compose.ui.text.input.KeyboardType  Number +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  Number 5androidx.compose.ui.text.input.KeyboardType.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  NotificationCompat androidx.core.app  	Alignment #androidx.core.app.ComponentActivity  Arrangement #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  Column #androidx.core.app.ComponentActivity  
Composable #androidx.core.app.ComponentActivity  FocusFlowApp #androidx.core.app.ComponentActivity  FocusFlowTheme #androidx.core.app.ComponentActivity  
FontWeight #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LocalContext #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  OnboardingScreen #androidx.core.app.ComponentActivity  Spacer #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  	TextAlign #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  fillMaxWidth #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  height #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  sp #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_DEFAULT $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  AICoachUiState androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  BudgetCategory androidx.lifecycle.ViewModel  BudgetCategoryRepository androidx.lifecycle.ViewModel  
BudgetUiState androidx.lifecycle.ViewModel  ChatMessage androidx.lifecycle.ViewModel  Clock androidx.lifecycle.ViewModel  
CreditCard androidx.lifecycle.ViewModel  CreditCardRepository androidx.lifecycle.ViewModel  DashboardUiState androidx.lifecycle.ViewModel  DateTimeUnit androidx.lifecycle.ViewModel  DebtUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  	Exception androidx.lifecycle.ViewModel  Expense androidx.lifecycle.ViewModel  ExpenseRepository androidx.lifecycle.ViewModel  ExpenseUiState androidx.lifecycle.ViewModel  Flow androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  	LocalDate androidx.lifecycle.ViewModel  
LocalDateTime androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MainUiState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  OnboardingStep androidx.lifecycle.ViewModel  OnboardingUiState androidx.lifecycle.ViewModel  Pair androidx.lifecycle.ViewModel  PayoffCalculation androidx.lifecycle.ViewModel  
PayoffStep androidx.lifecycle.ViewModel  PayoffStrategy androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  System androidx.lifecycle.ViewModel  TimeZone androidx.lifecycle.ViewModel  Triple androidx.lifecycle.ViewModel  UserPreferencesRepository androidx.lifecycle.ViewModel  _uiState androidx.lifecycle.ViewModel  addBudgetCategory androidx.lifecycle.ViewModel  
addCreditCard androidx.lifecycle.ViewModel  
addExpense androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  atTime androidx.lifecycle.ViewModel  budgetCategoryRepository androidx.lifecycle.ViewModel  checkOnboardingStatus androidx.lifecycle.ViewModel  
clearError androidx.lifecycle.ViewModel  
coerceAtLeast androidx.lifecycle.ViewModel  coerceAtMost androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  completeOnboarding androidx.lifecycle.ViewModel  contains androidx.lifecycle.ViewModel  creditCardRepository androidx.lifecycle.ViewModel  delay androidx.lifecycle.ViewModel  deleteBudgetCategory androidx.lifecycle.ViewModel  deleteCreditCard androidx.lifecycle.ViewModel  
deleteExpense androidx.lifecycle.ViewModel  expenseRepository androidx.lifecycle.ViewModel  firstOrNull androidx.lifecycle.ViewModel  format androidx.lifecycle.ViewModel  gamificationService androidx.lifecycle.ViewModel  generateAIResponse androidx.lifecycle.ViewModel  generateBudgetAdvice androidx.lifecycle.ViewModel  generateDebtAdvice androidx.lifecycle.ViewModel  generateGeneralResponse androidx.lifecycle.ViewModel  generateMoneyTip androidx.lifecycle.ViewModel  generatePayoffPlan androidx.lifecycle.ViewModel  generateProgressReport androidx.lifecycle.ViewModel  generateSpendingAnalysis androidx.lifecycle.ViewModel  generateTaskBreakdown androidx.lifecycle.ViewModel  getCurrentPeriodDates androidx.lifecycle.ViewModel  getCurrentPeriodValues androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  launch androidx.lifecycle.ViewModel  listOf androidx.lifecycle.ViewModel  loadBudgetData androidx.lifecycle.ViewModel  loadCurrentPeriodExpenses androidx.lifecycle.ViewModel  loadDashboardData androidx.lifecycle.ViewModel  loadDebtData androidx.lifecycle.ViewModel  	lowercase androidx.lifecycle.ViewModel  makePayment androidx.lifecycle.ViewModel  minus androidx.lifecycle.ViewModel  minusAssign androidx.lifecycle.ViewModel  nextStep androidx.lifecycle.ViewModel  plus androidx.lifecycle.ViewModel  
plusAssign androidx.lifecycle.ViewModel  random androidx.lifecycle.ViewModel  sendMessage androidx.lifecycle.ViewModel  sumOf androidx.lifecycle.ViewModel  toDoubleOrNull androidx.lifecycle.ViewModel  toLocalDateTime androidx.lifecycle.ViewModel  
trimIndent androidx.lifecycle.ViewModel  updateCurrentMessage androidx.lifecycle.ViewModel  updateFinancialGoals androidx.lifecycle.ViewModel  
updateHasDebt androidx.lifecycle.ViewModel  updateMonthlyIncome androidx.lifecycle.ViewModel  updateNotificationSettings androidx.lifecycle.ViewModel  updatePersonalGoals androidx.lifecycle.ViewModel  updateWeeklyBudget androidx.lifecycle.ViewModel  userPreferencesRepository androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  NavBackStackEntry androidx.navigation  
NavController androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  currentBackStackEntryAsState !androidx.navigation.NavController  getCURRENTBackStackEntryAsState !androidx.navigation.NavController  getCurrentBackStackEntryAsState !androidx.navigation.NavController  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  route "androidx.navigation.NavDestination  startDestinationId androidx.navigation.NavGraph  
AICoachScreen #androidx.navigation.NavGraphBuilder  BudgetScreen #androidx.navigation.NavGraphBuilder  DashboardScreen #androidx.navigation.NavGraphBuilder  
DebtScreen #androidx.navigation.NavGraphBuilder  ExpensesScreen #androidx.navigation.NavGraphBuilder  HabitsScreen #androidx.navigation.NavGraphBuilder  Screen #androidx.navigation.NavGraphBuilder  TasksScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  invoke %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  kotlinx 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AIInteractionDao androidx.room.RoomDatabase  AchievementDao androidx.room.RoomDatabase  BudgetCategoryDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  
CreditCardDao androidx.room.RoomDatabase  
ExpenseDao androidx.room.RoomDatabase  FocusFlowDatabase androidx.room.RoomDatabase  HabitLogDao androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  TaskDao androidx.room.RoomDatabase  UserPreferencesDao androidx.room.RoomDatabase  UserStatsDao androidx.room.RoomDatabase  
VirtualPetDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  achievementDao androidx.room.RoomDatabase  aiInteractionDao androidx.room.RoomDatabase  budgetCategoryDao androidx.room.RoomDatabase  
creditCardDao androidx.room.RoomDatabase  
expenseDao androidx.room.RoomDatabase  habitLogDao androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  taskDao androidx.room.RoomDatabase  userPreferencesDao androidx.room.RoomDatabase  userStatsDao androidx.room.RoomDatabase  
virtualPetDao androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  FocusFlowDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  getSYNCHRONIZED $androidx.room.RoomDatabase.Companion  getSynchronized $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  
AICoachScreen 
com.focusflow  Box 
com.focusflow  BudgetScreen 
com.focusflow  CircularProgressIndicator 
com.focusflow  
Composable 
com.focusflow  DashboardScreen 
com.focusflow  
DebtScreen 
com.focusflow  ExpensesScreen 
com.focusflow  FocusFlowApp 
com.focusflow  FocusFlowApplication 
com.focusflow  FocusFlowTheme 
com.focusflow  HabitsScreen 
com.focusflow  MainActivity 
com.focusflow  MainAppContent 
com.focusflow  R 
com.focusflow  Scaffold 
com.focusflow  Screen 
com.focusflow  TasksScreen 
com.focusflow  
composable 
com.focusflow  fillMaxSize 
com.focusflow  getValue 
com.focusflow  padding 
com.focusflow  provideDelegate 
com.focusflow  
setContent 
com.focusflow  Bundle com.focusflow.MainActivity  FocusFlowApp com.focusflow.MainActivity  FocusFlowTheme com.focusflow.MainActivity  
getSETContent com.focusflow.MainActivity  
getSetContent com.focusflow.MainActivity  
setContent com.focusflow.MainActivity  
AIInteraction com.focusflow.data.dao  AIInteractionDao com.focusflow.data.dao  Achievement com.focusflow.data.dao  AchievementDao com.focusflow.data.dao  Boolean com.focusflow.data.dao  BudgetCategory com.focusflow.data.dao  BudgetCategoryDao com.focusflow.data.dao  
Converters com.focusflow.data.dao  
CreditCard com.focusflow.data.dao  
CreditCardDao com.focusflow.data.dao  Dao com.focusflow.data.dao  Delete com.focusflow.data.dao  Double com.focusflow.data.dao  Expense com.focusflow.data.dao  
ExpenseDao com.focusflow.data.dao  FocusFlowDatabase com.focusflow.data.dao  HabitLog com.focusflow.data.dao  HabitLogDao com.focusflow.data.dao  Insert com.focusflow.data.dao  Int com.focusflow.data.dao  List com.focusflow.data.dao  Long com.focusflow.data.dao  OnConflictStrategy com.focusflow.data.dao  Query com.focusflow.data.dao  Room com.focusflow.data.dao  SingletonComponent com.focusflow.data.dao  String com.focusflow.data.dao  Task com.focusflow.data.dao  TaskDao com.focusflow.data.dao  Update com.focusflow.data.dao  UserPreferences com.focusflow.data.dao  UserPreferencesDao com.focusflow.data.dao  	UserStats com.focusflow.data.dao  UserStatsDao com.focusflow.data.dao  
VirtualPet com.focusflow.data.dao  
VirtualPetDao com.focusflow.data.dao  Volatile com.focusflow.data.dao  java com.focusflow.data.dao  kotlinx com.focusflow.data.dao  synchronized com.focusflow.data.dao  
AIInteraction 'com.focusflow.data.dao.AIInteractionDao  Delete 'com.focusflow.data.dao.AIInteractionDao  Flow 'com.focusflow.data.dao.AIInteractionDao  Insert 'com.focusflow.data.dao.AIInteractionDao  Int 'com.focusflow.data.dao.AIInteractionDao  List 'com.focusflow.data.dao.AIInteractionDao  Long 'com.focusflow.data.dao.AIInteractionDao  Query 'com.focusflow.data.dao.AIInteractionDao  String 'com.focusflow.data.dao.AIInteractionDao  kotlinx 'com.focusflow.data.dao.AIInteractionDao  Achievement %com.focusflow.data.dao.AchievementDao  Flow %com.focusflow.data.dao.AchievementDao  Insert %com.focusflow.data.dao.AchievementDao  Int %com.focusflow.data.dao.AchievementDao  List %com.focusflow.data.dao.AchievementDao  Long %com.focusflow.data.dao.AchievementDao  OnConflictStrategy %com.focusflow.data.dao.AchievementDao  Query %com.focusflow.data.dao.AchievementDao  String %com.focusflow.data.dao.AchievementDao  Update %com.focusflow.data.dao.AchievementDao  insertAchievement %com.focusflow.data.dao.AchievementDao  kotlinx %com.focusflow.data.dao.AchievementDao  BudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  Delete (com.focusflow.data.dao.BudgetCategoryDao  Double (com.focusflow.data.dao.BudgetCategoryDao  Flow (com.focusflow.data.dao.BudgetCategoryDao  Insert (com.focusflow.data.dao.BudgetCategoryDao  Int (com.focusflow.data.dao.BudgetCategoryDao  List (com.focusflow.data.dao.BudgetCategoryDao  Long (com.focusflow.data.dao.BudgetCategoryDao  Query (com.focusflow.data.dao.BudgetCategoryDao  String (com.focusflow.data.dao.BudgetCategoryDao  Update (com.focusflow.data.dao.BudgetCategoryDao  deleteBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  getAllBudgetCategories (com.focusflow.data.dao.BudgetCategoryDao  getBudgetCategoriesByPeriod (com.focusflow.data.dao.BudgetCategoryDao  getBudgetCategoryByName (com.focusflow.data.dao.BudgetCategoryDao  getTotalBudgetForPeriod (com.focusflow.data.dao.BudgetCategoryDao  getTotalSpentForPeriod (com.focusflow.data.dao.BudgetCategoryDao  insertBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateBudgetCategory (com.focusflow.data.dao.BudgetCategoryDao  updateSpentAmount (com.focusflow.data.dao.BudgetCategoryDao  
CreditCard $com.focusflow.data.dao.CreditCardDao  Delete $com.focusflow.data.dao.CreditCardDao  Double $com.focusflow.data.dao.CreditCardDao  Flow $com.focusflow.data.dao.CreditCardDao  Insert $com.focusflow.data.dao.CreditCardDao  List $com.focusflow.data.dao.CreditCardDao  	LocalDate $com.focusflow.data.dao.CreditCardDao  Long $com.focusflow.data.dao.CreditCardDao  Query $com.focusflow.data.dao.CreditCardDao  Update $com.focusflow.data.dao.CreditCardDao  deactivateCreditCard $com.focusflow.data.dao.CreditCardDao  deleteCreditCard $com.focusflow.data.dao.CreditCardDao  getAllActiveCreditCards $com.focusflow.data.dao.CreditCardDao  getCardsWithPaymentsDue $com.focusflow.data.dao.CreditCardDao  getCreditCardById $com.focusflow.data.dao.CreditCardDao  getTotalDebt $com.focusflow.data.dao.CreditCardDao  getTotalMinimumPaymentsDue $com.focusflow.data.dao.CreditCardDao  insertCreditCard $com.focusflow.data.dao.CreditCardDao  updateCreditCard $com.focusflow.data.dao.CreditCardDao  Delete !com.focusflow.data.dao.ExpenseDao  Double !com.focusflow.data.dao.ExpenseDao  Expense !com.focusflow.data.dao.ExpenseDao  Flow !com.focusflow.data.dao.ExpenseDao  Insert !com.focusflow.data.dao.ExpenseDao  List !com.focusflow.data.dao.ExpenseDao  
LocalDateTime !com.focusflow.data.dao.ExpenseDao  Long !com.focusflow.data.dao.ExpenseDao  Query !com.focusflow.data.dao.ExpenseDao  String !com.focusflow.data.dao.ExpenseDao  Update !com.focusflow.data.dao.ExpenseDao  
deleteExpense !com.focusflow.data.dao.ExpenseDao  deleteExpenseById !com.focusflow.data.dao.ExpenseDao  getAllCategories !com.focusflow.data.dao.ExpenseDao  getAllExpenses !com.focusflow.data.dao.ExpenseDao  getExpensesByCategory !com.focusflow.data.dao.ExpenseDao  getExpensesByDateRange !com.focusflow.data.dao.ExpenseDao  getTotalSpentByCategoryInPeriod !com.focusflow.data.dao.ExpenseDao  getTotalSpentInPeriod !com.focusflow.data.dao.ExpenseDao  
insertExpense !com.focusflow.data.dao.ExpenseDao  
updateExpense !com.focusflow.data.dao.ExpenseDao  Delete "com.focusflow.data.dao.HabitLogDao  Flow "com.focusflow.data.dao.HabitLogDao  HabitLog "com.focusflow.data.dao.HabitLogDao  Insert "com.focusflow.data.dao.HabitLogDao  Int "com.focusflow.data.dao.HabitLogDao  List "com.focusflow.data.dao.HabitLogDao  	LocalDate "com.focusflow.data.dao.HabitLogDao  Long "com.focusflow.data.dao.HabitLogDao  OnConflictStrategy "com.focusflow.data.dao.HabitLogDao  Query "com.focusflow.data.dao.HabitLogDao  String "com.focusflow.data.dao.HabitLogDao  Update "com.focusflow.data.dao.HabitLogDao  Delete com.focusflow.data.dao.TaskDao  Flow com.focusflow.data.dao.TaskDao  Insert com.focusflow.data.dao.TaskDao  Int com.focusflow.data.dao.TaskDao  List com.focusflow.data.dao.TaskDao  
LocalDateTime com.focusflow.data.dao.TaskDao  Long com.focusflow.data.dao.TaskDao  Query com.focusflow.data.dao.TaskDao  String com.focusflow.data.dao.TaskDao  Task com.focusflow.data.dao.TaskDao  Update com.focusflow.data.dao.TaskDao  Boolean )com.focusflow.data.dao.UserPreferencesDao  Flow )com.focusflow.data.dao.UserPreferencesDao  Insert )com.focusflow.data.dao.UserPreferencesDao  OnConflictStrategy )com.focusflow.data.dao.UserPreferencesDao  Query )com.focusflow.data.dao.UserPreferencesDao  String )com.focusflow.data.dao.UserPreferencesDao  Update )com.focusflow.data.dao.UserPreferencesDao  UserPreferences )com.focusflow.data.dao.UserPreferencesDao  getUserPreferences )com.focusflow.data.dao.UserPreferencesDao  getUserPreferencesSync )com.focusflow.data.dao.UserPreferencesDao  insertUserPreferences )com.focusflow.data.dao.UserPreferencesDao  updateBudgetPeriod )com.focusflow.data.dao.UserPreferencesDao  updateDarkModeEnabled )com.focusflow.data.dao.UserPreferencesDao  updateFontSize )com.focusflow.data.dao.UserPreferencesDao  updateNotificationsEnabled )com.focusflow.data.dao.UserPreferencesDao  updateUserPreferences )com.focusflow.data.dao.UserPreferencesDao  Double #com.focusflow.data.dao.UserStatsDao  Flow #com.focusflow.data.dao.UserStatsDao  Insert #com.focusflow.data.dao.UserStatsDao  Int #com.focusflow.data.dao.UserStatsDao  OnConflictStrategy #com.focusflow.data.dao.UserStatsDao  Query #com.focusflow.data.dao.UserStatsDao  Update #com.focusflow.data.dao.UserStatsDao  	UserStats #com.focusflow.data.dao.UserStatsDao  addDebtPaid #com.focusflow.data.dao.UserStatsDao  	addPoints #com.focusflow.data.dao.UserStatsDao  getUserStatsSync #com.focusflow.data.dao.UserStatsDao  incrementExpensesLogged #com.focusflow.data.dao.UserStatsDao  insertUserStats #com.focusflow.data.dao.UserStatsDao  updateBudgetAdherenceStreak #com.focusflow.data.dao.UserStatsDao  updateExpenseLoggingStreak #com.focusflow.data.dao.UserStatsDao  updateUserStats #com.focusflow.data.dao.UserStatsDao  Flow $com.focusflow.data.dao.VirtualPetDao  Insert $com.focusflow.data.dao.VirtualPetDao  Int $com.focusflow.data.dao.VirtualPetDao  OnConflictStrategy $com.focusflow.data.dao.VirtualPetDao  Query $com.focusflow.data.dao.VirtualPetDao  Update $com.focusflow.data.dao.VirtualPetDao  
VirtualPet $com.focusflow.data.dao.VirtualPetDao  getVirtualPetSync $com.focusflow.data.dao.VirtualPetDao  insertVirtualPet $com.focusflow.data.dao.VirtualPetDao  kotlinx $com.focusflow.data.dao.VirtualPetDao  updateVirtualPet $com.focusflow.data.dao.VirtualPetDao  
AIInteraction com.focusflow.data.database  AIInteractionDao com.focusflow.data.database  Achievement com.focusflow.data.database  AchievementDao com.focusflow.data.database  BudgetCategory com.focusflow.data.database  BudgetCategoryDao com.focusflow.data.database  
Converters com.focusflow.data.database  
CreditCard com.focusflow.data.database  
CreditCardDao com.focusflow.data.database  Expense com.focusflow.data.database  
ExpenseDao com.focusflow.data.database  FocusFlowDatabase com.focusflow.data.database  HabitLog com.focusflow.data.database  HabitLogDao com.focusflow.data.database  	LocalDate com.focusflow.data.database  
LocalDateTime com.focusflow.data.database  Room com.focusflow.data.database  String com.focusflow.data.database  Task com.focusflow.data.database  TaskDao com.focusflow.data.database  UserPreferences com.focusflow.data.database  UserPreferencesDao com.focusflow.data.database  	UserStats com.focusflow.data.database  UserStatsDao com.focusflow.data.database  
VirtualPet com.focusflow.data.database  
VirtualPetDao com.focusflow.data.database  Volatile com.focusflow.data.database  java com.focusflow.data.database  let com.focusflow.data.database  synchronized com.focusflow.data.database  	LocalDate &com.focusflow.data.database.Converters  
LocalDateTime &com.focusflow.data.database.Converters  String &com.focusflow.data.database.Converters  
TypeConverter &com.focusflow.data.database.Converters  getLET &com.focusflow.data.database.Converters  getLet &com.focusflow.data.database.Converters  let &com.focusflow.data.database.Converters  AIInteractionDao -com.focusflow.data.database.FocusFlowDatabase  AchievementDao -com.focusflow.data.database.FocusFlowDatabase  BudgetCategoryDao -com.focusflow.data.database.FocusFlowDatabase  	Companion -com.focusflow.data.database.FocusFlowDatabase  Context -com.focusflow.data.database.FocusFlowDatabase  
CreditCardDao -com.focusflow.data.database.FocusFlowDatabase  
ExpenseDao -com.focusflow.data.database.FocusFlowDatabase  FocusFlowDatabase -com.focusflow.data.database.FocusFlowDatabase  HabitLogDao -com.focusflow.data.database.FocusFlowDatabase  Room -com.focusflow.data.database.FocusFlowDatabase  TaskDao -com.focusflow.data.database.FocusFlowDatabase  UserPreferencesDao -com.focusflow.data.database.FocusFlowDatabase  UserStatsDao -com.focusflow.data.database.FocusFlowDatabase  
VirtualPetDao -com.focusflow.data.database.FocusFlowDatabase  Volatile -com.focusflow.data.database.FocusFlowDatabase  achievementDao -com.focusflow.data.database.FocusFlowDatabase  aiInteractionDao -com.focusflow.data.database.FocusFlowDatabase  budgetCategoryDao -com.focusflow.data.database.FocusFlowDatabase  
creditCardDao -com.focusflow.data.database.FocusFlowDatabase  
expenseDao -com.focusflow.data.database.FocusFlowDatabase  habitLogDao -com.focusflow.data.database.FocusFlowDatabase  java -com.focusflow.data.database.FocusFlowDatabase  synchronized -com.focusflow.data.database.FocusFlowDatabase  taskDao -com.focusflow.data.database.FocusFlowDatabase  userPreferencesDao -com.focusflow.data.database.FocusFlowDatabase  userStatsDao -com.focusflow.data.database.FocusFlowDatabase  
virtualPetDao -com.focusflow.data.database.FocusFlowDatabase  AIInteractionDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  AchievementDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  BudgetCategoryDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Context 7com.focusflow.data.database.FocusFlowDatabase.Companion  
CreditCardDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
ExpenseDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  FocusFlowDatabase 7com.focusflow.data.database.FocusFlowDatabase.Companion  HabitLogDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  INSTANCE 7com.focusflow.data.database.FocusFlowDatabase.Companion  Room 7com.focusflow.data.database.FocusFlowDatabase.Companion  TaskDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserPreferencesDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  UserStatsDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  
VirtualPetDao 7com.focusflow.data.database.FocusFlowDatabase.Companion  Volatile 7com.focusflow.data.database.FocusFlowDatabase.Companion  getSYNCHRONIZED 7com.focusflow.data.database.FocusFlowDatabase.Companion  getSynchronized 7com.focusflow.data.database.FocusFlowDatabase.Companion  java 7com.focusflow.data.database.FocusFlowDatabase.Companion  synchronized 7com.focusflow.data.database.FocusFlowDatabase.Companion  
AIInteraction com.focusflow.data.model  AIInteractionDao com.focusflow.data.model  Achievement com.focusflow.data.model  AchievementDao com.focusflow.data.model  Boolean com.focusflow.data.model  BudgetCategory com.focusflow.data.model  BudgetCategoryDao com.focusflow.data.model  
Converters com.focusflow.data.model  
CreditCard com.focusflow.data.model  
CreditCardDao com.focusflow.data.model  Double com.focusflow.data.model  Expense com.focusflow.data.model  
ExpenseDao com.focusflow.data.model  FocusFlowDatabase com.focusflow.data.model  HabitLog com.focusflow.data.model  HabitLogDao com.focusflow.data.model  Income com.focusflow.data.model  Int com.focusflow.data.model  List com.focusflow.data.model  Long com.focusflow.data.model  Room com.focusflow.data.model  String com.focusflow.data.model  StringListConverter com.focusflow.data.model  Task com.focusflow.data.model  TaskDao com.focusflow.data.model  UserPreferences com.focusflow.data.model  UserPreferencesDao com.focusflow.data.model  	UserStats com.focusflow.data.model  UserStatsDao com.focusflow.data.model  
VirtualPet com.focusflow.data.model  
VirtualPetDao com.focusflow.data.model  Volatile com.focusflow.data.model  	emptyList com.focusflow.data.model  isEmpty com.focusflow.data.model  java com.focusflow.data.model  joinToString com.focusflow.data.model  split com.focusflow.data.model  synchronized com.focusflow.data.model  
LocalDateTime &com.focusflow.data.model.AIInteraction  Long &com.focusflow.data.model.AIInteraction  
PrimaryKey &com.focusflow.data.model.AIInteraction  String &com.focusflow.data.model.AIInteraction  Boolean $com.focusflow.data.model.Achievement  Int $com.focusflow.data.model.Achievement  
LocalDateTime $com.focusflow.data.model.Achievement  Long $com.focusflow.data.model.Achievement  
PrimaryKey $com.focusflow.data.model.Achievement  String $com.focusflow.data.model.Achievement  Boolean 'com.focusflow.data.model.BudgetCategory  Double 'com.focusflow.data.model.BudgetCategory  Int 'com.focusflow.data.model.BudgetCategory  Long 'com.focusflow.data.model.BudgetCategory  
PrimaryKey 'com.focusflow.data.model.BudgetCategory  String 'com.focusflow.data.model.BudgetCategory  allocatedAmount 'com.focusflow.data.model.BudgetCategory  name 'com.focusflow.data.model.BudgetCategory  spentAmount 'com.focusflow.data.model.BudgetCategory  Boolean #com.focusflow.data.model.CreditCard  Double #com.focusflow.data.model.CreditCard  	LocalDate #com.focusflow.data.model.CreditCard  Long #com.focusflow.data.model.CreditCard  
PrimaryKey #com.focusflow.data.model.CreditCard  String #com.focusflow.data.model.CreditCard  copy #com.focusflow.data.model.CreditCard  creditLimit #com.focusflow.data.model.CreditCard  currentBalance #com.focusflow.data.model.CreditCard  dueDate #com.focusflow.data.model.CreditCard  equals #com.focusflow.data.model.CreditCard  getLET #com.focusflow.data.model.CreditCard  getLet #com.focusflow.data.model.CreditCard  id #com.focusflow.data.model.CreditCard  let #com.focusflow.data.model.CreditCard  minimumPayment #com.focusflow.data.model.CreditCard  name #com.focusflow.data.model.CreditCard  Boolean  com.focusflow.data.model.Expense  Double  com.focusflow.data.model.Expense  
LocalDateTime  com.focusflow.data.model.Expense  Long  com.focusflow.data.model.Expense  
PrimaryKey  com.focusflow.data.model.Expense  String  com.focusflow.data.model.Expense  amount  com.focusflow.data.model.Expense  category  com.focusflow.data.model.Expense  date  com.focusflow.data.model.Expense  description  com.focusflow.data.model.Expense  merchant  com.focusflow.data.model.Expense  	LocalDate !com.focusflow.data.model.HabitLog  Long !com.focusflow.data.model.HabitLog  
PrimaryKey !com.focusflow.data.model.HabitLog  String !com.focusflow.data.model.HabitLog  Boolean com.focusflow.data.model.Income  Double com.focusflow.data.model.Income  	LocalDate com.focusflow.data.model.Income  Long com.focusflow.data.model.Income  
PrimaryKey com.focusflow.data.model.Income  String com.focusflow.data.model.Income  List ,com.focusflow.data.model.StringListConverter  String ,com.focusflow.data.model.StringListConverter  
TypeConverter ,com.focusflow.data.model.StringListConverter  	emptyList ,com.focusflow.data.model.StringListConverter  getEMPTYList ,com.focusflow.data.model.StringListConverter  getEmptyList ,com.focusflow.data.model.StringListConverter  
getISEmpty ,com.focusflow.data.model.StringListConverter  
getIsEmpty ,com.focusflow.data.model.StringListConverter  getJOINToString ,com.focusflow.data.model.StringListConverter  getJoinToString ,com.focusflow.data.model.StringListConverter  getSPLIT ,com.focusflow.data.model.StringListConverter  getSplit ,com.focusflow.data.model.StringListConverter  isEmpty ,com.focusflow.data.model.StringListConverter  joinToString ,com.focusflow.data.model.StringListConverter  split ,com.focusflow.data.model.StringListConverter  Boolean com.focusflow.data.model.Task  Int com.focusflow.data.model.Task  
LocalDateTime com.focusflow.data.model.Task  Long com.focusflow.data.model.Task  
PrimaryKey com.focusflow.data.model.Task  String com.focusflow.data.model.Task  Boolean (com.focusflow.data.model.UserPreferences  Double (com.focusflow.data.model.UserPreferences  Long (com.focusflow.data.model.UserPreferences  
PrimaryKey (com.focusflow.data.model.UserPreferences  String (com.focusflow.data.model.UserPreferences  budgetPeriod (com.focusflow.data.model.UserPreferences  copy (com.focusflow.data.model.UserPreferences  hasCompletedOnboarding (com.focusflow.data.model.UserPreferences  
monthlyBudget (com.focusflow.data.model.UserPreferences  weeklyBudget (com.focusflow.data.model.UserPreferences  Double "com.focusflow.data.model.UserStats  Int "com.focusflow.data.model.UserStats  
LocalDateTime "com.focusflow.data.model.UserStats  Long "com.focusflow.data.model.UserStats  
PrimaryKey "com.focusflow.data.model.UserStats  budgetAdherenceStreak "com.focusflow.data.model.UserStats  copy "com.focusflow.data.model.UserStats  currentLevel "com.focusflow.data.model.UserStats  equals "com.focusflow.data.model.UserStats  expenseLoggingStreak "com.focusflow.data.model.UserStats  lastActivityDate "com.focusflow.data.model.UserStats  totalExpensesLogged "com.focusflow.data.model.UserStats  totalPoints "com.focusflow.data.model.UserStats  Int #com.focusflow.data.model.VirtualPet  List #com.focusflow.data.model.VirtualPet  
LocalDateTime #com.focusflow.data.model.VirtualPet  Long #com.focusflow.data.model.VirtualPet  
PrimaryKey #com.focusflow.data.model.VirtualPet  String #com.focusflow.data.model.VirtualPet  copy #com.focusflow.data.model.VirtualPet  	emptyList #com.focusflow.data.model.VirtualPet  equals #com.focusflow.data.model.VirtualPet  
experience #com.focusflow.data.model.VirtualPet  	happiness #com.focusflow.data.model.VirtualPet  name #com.focusflow.data.model.VirtualPet  Boolean com.focusflow.data.repository  BudgetCategoryRepository com.focusflow.data.repository  CreditCardRepository com.focusflow.data.repository  Double com.focusflow.data.repository  ExpenseRepository com.focusflow.data.repository  List com.focusflow.data.repository  Long com.focusflow.data.repository  String com.focusflow.data.repository  UserPreferencesRepository com.focusflow.data.repository  BudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  BudgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  Double 6com.focusflow.data.repository.BudgetCategoryRepository  Flow 6com.focusflow.data.repository.BudgetCategoryRepository  Inject 6com.focusflow.data.repository.BudgetCategoryRepository  List 6com.focusflow.data.repository.BudgetCategoryRepository  Long 6com.focusflow.data.repository.BudgetCategoryRepository  String 6com.focusflow.data.repository.BudgetCategoryRepository  budgetCategoryDao 6com.focusflow.data.repository.BudgetCategoryRepository  deleteBudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  getBudgetCategoriesByPeriod 6com.focusflow.data.repository.BudgetCategoryRepository  insertBudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  updateBudgetCategory 6com.focusflow.data.repository.BudgetCategoryRepository  
CreditCard 2com.focusflow.data.repository.CreditCardRepository  
CreditCardDao 2com.focusflow.data.repository.CreditCardRepository  Double 2com.focusflow.data.repository.CreditCardRepository  Flow 2com.focusflow.data.repository.CreditCardRepository  Inject 2com.focusflow.data.repository.CreditCardRepository  List 2com.focusflow.data.repository.CreditCardRepository  	LocalDate 2com.focusflow.data.repository.CreditCardRepository  Long 2com.focusflow.data.repository.CreditCardRepository  
creditCardDao 2com.focusflow.data.repository.CreditCardRepository  deleteCreditCard 2com.focusflow.data.repository.CreditCardRepository  getAllActiveCreditCards 2com.focusflow.data.repository.CreditCardRepository  getCreditCardById 2com.focusflow.data.repository.CreditCardRepository  getTotalDebt 2com.focusflow.data.repository.CreditCardRepository  getTotalMinimumPaymentsDue 2com.focusflow.data.repository.CreditCardRepository  insertCreditCard 2com.focusflow.data.repository.CreditCardRepository  updateCreditCard 2com.focusflow.data.repository.CreditCardRepository  Double /com.focusflow.data.repository.ExpenseRepository  Expense /com.focusflow.data.repository.ExpenseRepository  
ExpenseDao /com.focusflow.data.repository.ExpenseRepository  Flow /com.focusflow.data.repository.ExpenseRepository  Inject /com.focusflow.data.repository.ExpenseRepository  List /com.focusflow.data.repository.ExpenseRepository  
LocalDateTime /com.focusflow.data.repository.ExpenseRepository  Long /com.focusflow.data.repository.ExpenseRepository  String /com.focusflow.data.repository.ExpenseRepository  
deleteExpense /com.focusflow.data.repository.ExpenseRepository  
expenseDao /com.focusflow.data.repository.ExpenseRepository  getAllCategories /com.focusflow.data.repository.ExpenseRepository  getAllExpenses /com.focusflow.data.repository.ExpenseRepository  getExpensesByCategory /com.focusflow.data.repository.ExpenseRepository  getExpensesByDateRange /com.focusflow.data.repository.ExpenseRepository  getTotalSpentInPeriod /com.focusflow.data.repository.ExpenseRepository  
insertExpense /com.focusflow.data.repository.ExpenseRepository  
updateExpense /com.focusflow.data.repository.ExpenseRepository  Boolean 7com.focusflow.data.repository.UserPreferencesRepository  Flow 7com.focusflow.data.repository.UserPreferencesRepository  Inject 7com.focusflow.data.repository.UserPreferencesRepository  String 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferences 7com.focusflow.data.repository.UserPreferencesRepository  UserPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  getUserPreferencesSync 7com.focusflow.data.repository.UserPreferencesRepository  insertUserPreferences 7com.focusflow.data.repository.UserPreferencesRepository  updateUserPreferences 7com.focusflow.data.repository.UserPreferencesRepository  userPreferencesDao 7com.focusflow.data.repository.UserPreferencesRepository  AIInteractionDao com.focusflow.di  AchievementDao com.focusflow.di  BudgetCategoryDao com.focusflow.di  
CreditCardDao com.focusflow.di  DatabaseModule com.focusflow.di  
ExpenseDao com.focusflow.di  FocusFlowDatabase com.focusflow.di  HabitLogDao com.focusflow.di  Room com.focusflow.di  SingletonComponent com.focusflow.di  TaskDao com.focusflow.di  UserPreferencesDao com.focusflow.di  UserStatsDao com.focusflow.di  
VirtualPetDao com.focusflow.di  java com.focusflow.di  AIInteractionDao com.focusflow.di.DatabaseModule  AchievementDao com.focusflow.di.DatabaseModule  ApplicationContext com.focusflow.di.DatabaseModule  BudgetCategoryDao com.focusflow.di.DatabaseModule  Context com.focusflow.di.DatabaseModule  
CreditCardDao com.focusflow.di.DatabaseModule  
ExpenseDao com.focusflow.di.DatabaseModule  FocusFlowDatabase com.focusflow.di.DatabaseModule  HabitLogDao com.focusflow.di.DatabaseModule  Provides com.focusflow.di.DatabaseModule  Room com.focusflow.di.DatabaseModule  	Singleton com.focusflow.di.DatabaseModule  TaskDao com.focusflow.di.DatabaseModule  UserPreferencesDao com.focusflow.di.DatabaseModule  UserStatsDao com.focusflow.di.DatabaseModule  
VirtualPetDao com.focusflow.di.DatabaseModule  java com.focusflow.di.DatabaseModule  CheckCircle com.focusflow.navigation  Favorite com.focusflow.navigation  Home com.focusflow.navigation  List com.focusflow.navigation  Person com.focusflow.navigation  Screen com.focusflow.navigation  ShoppingCart com.focusflow.navigation  Star com.focusflow.navigation  String com.focusflow.navigation  bottomNavItems com.focusflow.navigation  listOf com.focusflow.navigation  AICoach com.focusflow.navigation.Screen  Budget com.focusflow.navigation.Screen  CheckCircle com.focusflow.navigation.Screen  	Dashboard com.focusflow.navigation.Screen  Debt com.focusflow.navigation.Screen  Expenses com.focusflow.navigation.Screen  Favorite com.focusflow.navigation.Screen  Habits com.focusflow.navigation.Screen  Home com.focusflow.navigation.Screen  Icons com.focusflow.navigation.Screen  ImageVector com.focusflow.navigation.Screen  List com.focusflow.navigation.Screen  Person com.focusflow.navigation.Screen  Screen com.focusflow.navigation.Screen  ShoppingCart com.focusflow.navigation.Screen  Star com.focusflow.navigation.Screen  String com.focusflow.navigation.Screen  Tasks com.focusflow.navigation.Screen  icon com.focusflow.navigation.Screen  route com.focusflow.navigation.Screen  title com.focusflow.navigation.Screen  Icons 'com.focusflow.navigation.Screen.AICoach  Person 'com.focusflow.navigation.Screen.AICoach  route 'com.focusflow.navigation.Screen.AICoach  Icons &com.focusflow.navigation.Screen.Budget  List &com.focusflow.navigation.Screen.Budget  route &com.focusflow.navigation.Screen.Budget  Home )com.focusflow.navigation.Screen.Dashboard  Icons )com.focusflow.navigation.Screen.Dashboard  route )com.focusflow.navigation.Screen.Dashboard  Icons $com.focusflow.navigation.Screen.Debt  Star $com.focusflow.navigation.Screen.Debt  route $com.focusflow.navigation.Screen.Debt  Icons (com.focusflow.navigation.Screen.Expenses  ShoppingCart (com.focusflow.navigation.Screen.Expenses  route (com.focusflow.navigation.Screen.Expenses  Favorite &com.focusflow.navigation.Screen.Habits  Icons &com.focusflow.navigation.Screen.Habits  route &com.focusflow.navigation.Screen.Habits  CheckCircle %com.focusflow.navigation.Screen.Tasks  Icons %com.focusflow.navigation.Screen.Tasks  route %com.focusflow.navigation.Screen.Tasks  
AlarmReceiver com.focusflow.receiver  Intent com.focusflow.receiver  NotificationService com.focusflow.receiver  apply com.focusflow.receiver  java com.focusflow.receiver  Context $com.focusflow.receiver.AlarmReceiver  Intent $com.focusflow.receiver.AlarmReceiver  NotificationService $com.focusflow.receiver.AlarmReceiver  apply $com.focusflow.receiver.AlarmReceiver  getAPPLY $com.focusflow.receiver.AlarmReceiver  getApply $com.focusflow.receiver.AlarmReceiver  java $com.focusflow.receiver.AlarmReceiver  Achievement com.focusflow.service  Build com.focusflow.service  
CHANNEL_ID com.focusflow.service  Clock com.focusflow.service  Context com.focusflow.service  Double com.focusflow.service  GamificationService com.focusflow.service  Int com.focusflow.service  Intent com.focusflow.service  MainActivity com.focusflow.service  NOTIFICATION_ID com.focusflow.service  NotificationChannel com.focusflow.service  NotificationCompat com.focusflow.service  NotificationManager com.focusflow.service  NotificationService com.focusflow.service  
PendingIntent com.focusflow.service  START_NOT_STICKY com.focusflow.service  String com.focusflow.service  TimeZone com.focusflow.service  	UserStats com.focusflow.service  
VirtualPet com.focusflow.service  android com.focusflow.service  apply com.focusflow.service  createNotificationChannel com.focusflow.service  forEach com.focusflow.service  java com.focusflow.service  listOf com.focusflow.service  minOf com.focusflow.service  toLocalDateTime com.focusflow.service  Achievement )com.focusflow.service.GamificationService  AchievementDao )com.focusflow.service.GamificationService  Clock )com.focusflow.service.GamificationService  Double )com.focusflow.service.GamificationService  Inject )com.focusflow.service.GamificationService  Int )com.focusflow.service.GamificationService  String )com.focusflow.service.GamificationService  TimeZone )com.focusflow.service.GamificationService  	UserStats )com.focusflow.service.GamificationService  UserStatsDao )com.focusflow.service.GamificationService  
VirtualPet )com.focusflow.service.GamificationService  
VirtualPetDao )com.focusflow.service.GamificationService  achievementDao )com.focusflow.service.GamificationService  awardPoints )com.focusflow.service.GamificationService  calculateLevel )com.focusflow.service.GamificationService  calculatePetLevel )com.focusflow.service.GamificationService  checkBudgetAchievements )com.focusflow.service.GamificationService  checkDebtAchievements )com.focusflow.service.GamificationService  checkExpenseLoggingAchievements )com.focusflow.service.GamificationService  checkStreakAchievements )com.focusflow.service.GamificationService  feedVirtualPet )com.focusflow.service.GamificationService  	getLISTOf )com.focusflow.service.GamificationService  	getListOf )com.focusflow.service.GamificationService  getMINOf )com.focusflow.service.GamificationService  getMinOf )com.focusflow.service.GamificationService  getTOLocalDateTime )com.focusflow.service.GamificationService  getToLocalDateTime )com.focusflow.service.GamificationService  initializeAchievements )com.focusflow.service.GamificationService  initializeGamification )com.focusflow.service.GamificationService  listOf )com.focusflow.service.GamificationService  minOf )com.focusflow.service.GamificationService  onExpenseLogged )com.focusflow.service.GamificationService  toLocalDateTime )com.focusflow.service.GamificationService  userStatsDao )com.focusflow.service.GamificationService  
virtualPetDao )com.focusflow.service.GamificationService  Build )com.focusflow.service.NotificationService  
CHANNEL_ID )com.focusflow.service.NotificationService  	Companion )com.focusflow.service.NotificationService  Context )com.focusflow.service.NotificationService  IBinder )com.focusflow.service.NotificationService  Int )com.focusflow.service.NotificationService  Intent )com.focusflow.service.NotificationService  MainActivity )com.focusflow.service.NotificationService  NOTIFICATION_ID )com.focusflow.service.NotificationService  NotificationChannel )com.focusflow.service.NotificationService  NotificationCompat )com.focusflow.service.NotificationService  NotificationManager )com.focusflow.service.NotificationService  
PendingIntent )com.focusflow.service.NotificationService  START_NOT_STICKY )com.focusflow.service.NotificationService  String )com.focusflow.service.NotificationService  android )com.focusflow.service.NotificationService  apply )com.focusflow.service.NotificationService  createNotificationChannel )com.focusflow.service.NotificationService  
getANDROID )com.focusflow.service.NotificationService  getAPPLY )com.focusflow.service.NotificationService  
getAndroid )com.focusflow.service.NotificationService  getApply )com.focusflow.service.NotificationService  getCREATENotificationChannel )com.focusflow.service.NotificationService  getCreateNotificationChannel )com.focusflow.service.NotificationService  getSystemService )com.focusflow.service.NotificationService  java )com.focusflow.service.NotificationService  showNotification )com.focusflow.service.NotificationService  Build 3com.focusflow.service.NotificationService.Companion  
CHANNEL_ID 3com.focusflow.service.NotificationService.Companion  Context 3com.focusflow.service.NotificationService.Companion  IBinder 3com.focusflow.service.NotificationService.Companion  Int 3com.focusflow.service.NotificationService.Companion  Intent 3com.focusflow.service.NotificationService.Companion  MainActivity 3com.focusflow.service.NotificationService.Companion  NOTIFICATION_ID 3com.focusflow.service.NotificationService.Companion  NotificationChannel 3com.focusflow.service.NotificationService.Companion  NotificationCompat 3com.focusflow.service.NotificationService.Companion  NotificationManager 3com.focusflow.service.NotificationService.Companion  
PendingIntent 3com.focusflow.service.NotificationService.Companion  START_NOT_STICKY 3com.focusflow.service.NotificationService.Companion  String 3com.focusflow.service.NotificationService.Companion  android 3com.focusflow.service.NotificationService.Companion  apply 3com.focusflow.service.NotificationService.Companion  createNotificationChannel 3com.focusflow.service.NotificationService.Companion  
getANDROID 3com.focusflow.service.NotificationService.Companion  getAPPLY 3com.focusflow.service.NotificationService.Companion  
getAndroid 3com.focusflow.service.NotificationService.Companion  getApply 3com.focusflow.service.NotificationService.Companion  java 3com.focusflow.service.NotificationService.Companion  Add com.focusflow.ui.components  	Alignment com.focusflow.ui.components  Arrangement com.focusflow.ui.components  BottomNavigation com.focusflow.ui.components  BottomNavigationBar com.focusflow.ui.components  BottomNavigationItem com.focusflow.ui.components  BudgetWarningCard com.focusflow.ui.components  Button com.focusflow.ui.components  ButtonDefaults com.focusflow.ui.components  Card com.focusflow.ui.components  CheckCircle com.focusflow.ui.components  CircularProgressIndicator com.focusflow.ui.components  Color com.focusflow.ui.components  Column com.focusflow.ui.components  
Composable com.focusflow.ui.components  CooldownPeriodCard com.focusflow.ui.components  Double com.focusflow.ui.components  
FontWeight com.focusflow.ui.components  Icon com.focusflow.ui.components  Icons com.focusflow.ui.components  ImpulseControlDialog com.focusflow.ui.components  ImpulseControlQuestions com.focusflow.ui.components  Info com.focusflow.ui.components  Int com.focusflow.ui.components  LaunchedEffect com.focusflow.ui.components  LinearProgressIndicator com.focusflow.ui.components  
MaterialTheme com.focusflow.ui.components  Modifier com.focusflow.ui.components  OutlinedButton com.focusflow.ui.components  Row com.focusflow.ui.components  Spacer com.focusflow.ui.components  SpendingWatchlistCard com.focusflow.ui.components  Star com.focusflow.ui.components  String com.focusflow.ui.components  Text com.focusflow.ui.components  	TextAlign com.focusflow.ui.components  
TextButton com.focusflow.ui.components  Unit com.focusflow.ui.components  Warning com.focusflow.ui.components  bottomNavItems com.focusflow.ui.components  currentBackStackEntryAsState com.focusflow.ui.components  delay com.focusflow.ui.components  fillMaxWidth com.focusflow.ui.components  forEach com.focusflow.ui.components  format com.focusflow.ui.components  getValue com.focusflow.ui.components  height com.focusflow.ui.components  listOf com.focusflow.ui.components  mutableStateOf com.focusflow.ui.components  padding com.focusflow.ui.components  provideDelegate com.focusflow.ui.components  remember com.focusflow.ui.components  setValue com.focusflow.ui.components  size com.focusflow.ui.components  width com.focusflow.ui.components  ADHDFriendlyStep com.focusflow.ui.onboarding  
AccountCircle com.focusflow.ui.onboarding  	Alignment com.focusflow.ui.onboarding  Arrangement com.focusflow.ui.onboarding  Boolean com.focusflow.ui.onboarding  Box com.focusflow.ui.onboarding  BudgetSetupStep com.focusflow.ui.onboarding  Button com.focusflow.ui.onboarding  Card com.focusflow.ui.onboarding  CheckCircle com.focusflow.ui.onboarding  Checkbox com.focusflow.ui.onboarding  Column com.focusflow.ui.onboarding  CompleteStep com.focusflow.ui.onboarding  
Composable com.focusflow.ui.onboarding  
DebtSetupStep com.focusflow.ui.onboarding  Face com.focusflow.ui.onboarding  FavoriteBorder com.focusflow.ui.onboarding  FinancialGoalsStep com.focusflow.ui.onboarding  FocusFlowTheme com.focusflow.ui.onboarding  
FontWeight com.focusflow.ui.onboarding  Icon com.focusflow.ui.onboarding  IncomeSetupStep com.focusflow.ui.onboarding  Int com.focusflow.ui.onboarding  Intent com.focusflow.ui.onboarding  KeyboardOptions com.focusflow.ui.onboarding  KeyboardType com.focusflow.ui.onboarding  List com.focusflow.ui.onboarding  LocalContext com.focusflow.ui.onboarding  MainActivity com.focusflow.ui.onboarding  
MaterialTheme com.focusflow.ui.onboarding  Modifier com.focusflow.ui.onboarding  NotificationSetupStep com.focusflow.ui.onboarding  
Notifications com.focusflow.ui.onboarding  OnboardingActivity com.focusflow.ui.onboarding  OnboardingProgressIndicator com.focusflow.ui.onboarding  OnboardingScreen com.focusflow.ui.onboarding  OnboardingStep com.focusflow.ui.onboarding  OnboardingStepLayout com.focusflow.ui.onboarding  OutlinedTextField com.focusflow.ui.onboarding  Person com.focusflow.ui.onboarding  PersonalGoalsStep com.focusflow.ui.onboarding  RadioButton com.focusflow.ui.onboarding  RoundedCornerShape com.focusflow.ui.onboarding  Row com.focusflow.ui.onboarding  ShoppingCart com.focusflow.ui.onboarding  Spacer com.focusflow.ui.onboarding  Star com.focusflow.ui.onboarding  String com.focusflow.ui.onboarding  Surface com.focusflow.ui.onboarding  Switch com.focusflow.ui.onboarding  Text com.focusflow.ui.onboarding  	TextAlign com.focusflow.ui.onboarding  
TextButton com.focusflow.ui.onboarding  Unit com.focusflow.ui.onboarding  WelcomeStep com.focusflow.ui.onboarding  
background com.focusflow.ui.onboarding  	clickable com.focusflow.ui.onboarding  fillMaxSize com.focusflow.ui.onboarding  fillMaxWidth com.focusflow.ui.onboarding  finish com.focusflow.ui.onboarding  getValue com.focusflow.ui.onboarding  height com.focusflow.ui.onboarding  
isNotBlank com.focusflow.ui.onboarding  
isNotEmpty com.focusflow.ui.onboarding  items com.focusflow.ui.onboarding  java com.focusflow.ui.onboarding  launch com.focusflow.ui.onboarding  listOf com.focusflow.ui.onboarding  minus com.focusflow.ui.onboarding  padding com.focusflow.ui.onboarding  plus com.focusflow.ui.onboarding  provideDelegate com.focusflow.ui.onboarding  repeat com.focusflow.ui.onboarding  
setContent com.focusflow.ui.onboarding  size com.focusflow.ui.onboarding  toDoubleOrNull com.focusflow.ui.onboarding  width com.focusflow.ui.onboarding  	Alignment .com.focusflow.ui.onboarding.OnboardingActivity  Arrangement .com.focusflow.ui.onboarding.OnboardingActivity  Bundle .com.focusflow.ui.onboarding.OnboardingActivity  Button .com.focusflow.ui.onboarding.OnboardingActivity  Column .com.focusflow.ui.onboarding.OnboardingActivity  
Composable .com.focusflow.ui.onboarding.OnboardingActivity  FocusFlowTheme .com.focusflow.ui.onboarding.OnboardingActivity  
FontWeight .com.focusflow.ui.onboarding.OnboardingActivity  Intent .com.focusflow.ui.onboarding.OnboardingActivity  LocalContext .com.focusflow.ui.onboarding.OnboardingActivity  MainActivity .com.focusflow.ui.onboarding.OnboardingActivity  
MaterialTheme .com.focusflow.ui.onboarding.OnboardingActivity  Modifier .com.focusflow.ui.onboarding.OnboardingActivity  OnboardingScreen .com.focusflow.ui.onboarding.OnboardingActivity  Spacer .com.focusflow.ui.onboarding.OnboardingActivity  Surface .com.focusflow.ui.onboarding.OnboardingActivity  Text .com.focusflow.ui.onboarding.OnboardingActivity  	TextAlign .com.focusflow.ui.onboarding.OnboardingActivity  dp .com.focusflow.ui.onboarding.OnboardingActivity  fillMaxSize .com.focusflow.ui.onboarding.OnboardingActivity  fillMaxWidth .com.focusflow.ui.onboarding.OnboardingActivity  finish .com.focusflow.ui.onboarding.OnboardingActivity  getFILLMaxSize .com.focusflow.ui.onboarding.OnboardingActivity  getFILLMaxWidth .com.focusflow.ui.onboarding.OnboardingActivity  getFillMaxSize .com.focusflow.ui.onboarding.OnboardingActivity  getFillMaxWidth .com.focusflow.ui.onboarding.OnboardingActivity  	getHEIGHT .com.focusflow.ui.onboarding.OnboardingActivity  	getHeight .com.focusflow.ui.onboarding.OnboardingActivity  
getPADDING .com.focusflow.ui.onboarding.OnboardingActivity  
getPadding .com.focusflow.ui.onboarding.OnboardingActivity  
getSETContent .com.focusflow.ui.onboarding.OnboardingActivity  
getSetContent .com.focusflow.ui.onboarding.OnboardingActivity  height .com.focusflow.ui.onboarding.OnboardingActivity  java .com.focusflow.ui.onboarding.OnboardingActivity  padding .com.focusflow.ui.onboarding.OnboardingActivity  
setContent .com.focusflow.ui.onboarding.OnboardingActivity  sp .com.focusflow.ui.onboarding.OnboardingActivity  
AICoachScreen com.focusflow.ui.screens  AchievementBadge com.focusflow.ui.screens  Add com.focusflow.ui.screens  AddBudgetCategoryDialog com.focusflow.ui.screens  AddCreditCardDialog com.focusflow.ui.screens  AddExpenseDialog com.focusflow.ui.screens  AlertDialog com.focusflow.ui.screens  	Alignment com.focusflow.ui.screens  Arrangement com.focusflow.ui.screens  Boolean com.focusflow.ui.screens  Box com.focusflow.ui.screens  BudgetAmountItem com.focusflow.ui.screens  BudgetCategoryItem com.focusflow.ui.screens  BudgetOverviewCard com.focusflow.ui.screens  BudgetScreen com.focusflow.ui.screens  Button com.focusflow.ui.screens  ButtonDefaults com.focusflow.ui.screens  Card com.focusflow.ui.screens  Checkbox com.focusflow.ui.screens  CircleShape com.focusflow.ui.screens  CircularProgressIndicator com.focusflow.ui.screens  Color com.focusflow.ui.screens  Column com.focusflow.ui.screens  
Composable com.focusflow.ui.screens  CompositionLocalProvider com.focusflow.ui.screens  CreditCardItem com.focusflow.ui.screens  CreditCardSummaryCard com.focusflow.ui.screens  DashboardScreen com.focusflow.ui.screens  DebtOverviewCard com.focusflow.ui.screens  
DebtScreen com.focusflow.ui.screens  Delete com.focusflow.ui.screens  Double com.focusflow.ui.screens  Edit com.focusflow.ui.screens  EmptyBudgetState com.focusflow.ui.screens  EmptyStateCard com.focusflow.ui.screens  ExpenseCategories com.focusflow.ui.screens  ExpenseItem com.focusflow.ui.screens  ExpensesScreen com.focusflow.ui.screens  
FilterChip com.focusflow.ui.screens  FloatingActionButton com.focusflow.ui.screens  FocusFlowApp com.focusflow.ui.screens  FocusFlowTheme com.focusflow.ui.screens  
FontWeight com.focusflow.ui.screens  	GridCells com.focusflow.ui.screens  HabitStreakCard com.focusflow.ui.screens  HabitStreakItem com.focusflow.ui.screens  HabitsScreen com.focusflow.ui.screens  Icon com.focusflow.ui.screens  
IconButton com.focusflow.ui.screens  Icons com.focusflow.ui.screens  Int com.focusflow.ui.screens  KeyboardArrowRight com.focusflow.ui.screens  KeyboardArrowUp com.focusflow.ui.screens  KeyboardOptions com.focusflow.ui.screens  KeyboardType com.focusflow.ui.screens  LaunchedEffect com.focusflow.ui.screens  
LazyColumn com.focusflow.ui.screens  LazyRow com.focusflow.ui.screens  LazyVerticalGrid com.focusflow.ui.screens  LinearProgressIndicator com.focusflow.ui.screens  List com.focusflow.ui.screens  LocalContentColor com.focusflow.ui.screens  
MaterialTheme com.focusflow.ui.screens  
MessageBubble com.focusflow.ui.screens  MessageInputField com.focusflow.ui.screens  Modifier com.focusflow.ui.screens  MotivationalQuoteCard com.focusflow.ui.screens  OutlinedTextField com.focusflow.ui.screens  Pair com.focusflow.ui.screens  
PaymentDialog com.focusflow.ui.screens  PayoffPlannerDialog com.focusflow.ui.screens  PayoffStrategy com.focusflow.ui.screens  Person com.focusflow.ui.screens  ProgressAchievementsCard com.focusflow.ui.screens  QuickCategoryOverview com.focusflow.ui.screens  QuickSetupDialog com.focusflow.ui.screens  RadioButton com.focusflow.ui.screens  
RepeatMode com.focusflow.ui.screens  RoundedCornerShape com.focusflow.ui.screens  Row com.focusflow.ui.screens  SafeToSpendWidget com.focusflow.ui.screens  Scaffold com.focusflow.ui.screens  Screen com.focusflow.ui.screens  Send com.focusflow.ui.screens  ShoppingCart com.focusflow.ui.screens  Spacer com.focusflow.ui.screens  SpendingSummaryCard com.focusflow.ui.screens  Star com.focusflow.ui.screens  String com.focusflow.ui.screens  SuggestedPromptCard com.focusflow.ui.screens  SuggestedPromptsSection com.focusflow.ui.screens  Surface com.focusflow.ui.screens  System com.focusflow.ui.screens  TaskItem com.focusflow.ui.screens  TasksScreen com.focusflow.ui.screens  Text com.focusflow.ui.screens  	TextAlign com.focusflow.ui.screens  
TextButton com.focusflow.ui.screens  TodaysTasksCard com.focusflow.ui.screens  TypingIndicator com.focusflow.ui.screens  Unit com.focusflow.ui.screens  VirtualPetWidget com.focusflow.ui.screens  animateFloatAsState com.focusflow.ui.screens  
background com.focusflow.ui.screens  	clickable com.focusflow.ui.screens  coerceIn com.focusflow.ui.screens  
component1 com.focusflow.ui.screens  
component2 com.focusflow.ui.screens  
composable com.focusflow.ui.screens  	emptyList com.focusflow.ui.screens  fillMaxSize com.focusflow.ui.screens  fillMaxWidth com.focusflow.ui.screens  forEach com.focusflow.ui.screens  format com.focusflow.ui.screens  
formatDate com.focusflow.ui.screens  getValue com.focusflow.ui.screens  groupBy com.focusflow.ui.screens  height com.focusflow.ui.screens  infiniteRepeatable com.focusflow.ui.screens  
isNotBlank com.focusflow.ui.screens  
isNotEmpty com.focusflow.ui.screens  items com.focusflow.ui.screens  let com.focusflow.ui.screens  listOf com.focusflow.ui.screens  	lowercase com.focusflow.ui.screens  	mapValues com.focusflow.ui.screens  mutableStateOf com.focusflow.ui.screens  padding com.focusflow.ui.screens  provideDelegate com.focusflow.ui.screens  remember com.focusflow.ui.screens  repeat com.focusflow.ui.screens  replaceFirstChar com.focusflow.ui.screens  set com.focusflow.ui.screens  
setContent com.focusflow.ui.screens  setValue com.focusflow.ui.screens  size com.focusflow.ui.screens  sortedByDescending com.focusflow.ui.screens  sumOf com.focusflow.ui.screens  take com.focusflow.ui.screens  takeIf com.focusflow.ui.screens  toDoubleOrNull com.focusflow.ui.screens  toIntOrNull com.focusflow.ui.screens  toList com.focusflow.ui.screens  toMap com.focusflow.ui.screens  toMutableMap com.focusflow.ui.screens  tween com.focusflow.ui.screens  width com.focusflow.ui.screens  widthIn com.focusflow.ui.screens  Boolean com.focusflow.ui.theme  DarkColorPalette com.focusflow.ui.theme  FocusFlowTheme com.focusflow.ui.theme  LightColorPalette com.focusflow.ui.theme  	Purple200 com.focusflow.ui.theme  	Purple500 com.focusflow.ui.theme  	Purple700 com.focusflow.ui.theme  Shapes com.focusflow.ui.theme  Teal200 com.focusflow.ui.theme  
Typography com.focusflow.ui.theme  Unit com.focusflow.ui.theme  AICoachUiState com.focusflow.ui.viewmodel  AICoachViewModel com.focusflow.ui.viewmodel  Boolean com.focusflow.ui.viewmodel  BudgetCategory com.focusflow.ui.viewmodel  
BudgetUiState com.focusflow.ui.viewmodel  BudgetViewModel com.focusflow.ui.viewmodel  ChatMessage com.focusflow.ui.viewmodel  Clock com.focusflow.ui.viewmodel  
CreditCard com.focusflow.ui.viewmodel  DashboardUiState com.focusflow.ui.viewmodel  DashboardViewModel com.focusflow.ui.viewmodel  DateTimeUnit com.focusflow.ui.viewmodel  DebtUiState com.focusflow.ui.viewmodel  
DebtViewModel com.focusflow.ui.viewmodel  DefaultBudgetCategories com.focusflow.ui.viewmodel  Double com.focusflow.ui.viewmodel  	Exception com.focusflow.ui.viewmodel  Expense com.focusflow.ui.viewmodel  ExpenseCategories com.focusflow.ui.viewmodel  ExpenseUiState com.focusflow.ui.viewmodel  ExpenseViewModel com.focusflow.ui.viewmodel  Flow com.focusflow.ui.viewmodel  Int com.focusflow.ui.viewmodel  List com.focusflow.ui.viewmodel  	LocalDate com.focusflow.ui.viewmodel  
LocalDateTime com.focusflow.ui.viewmodel  Long com.focusflow.ui.viewmodel  MainUiState com.focusflow.ui.viewmodel  
MainViewModel com.focusflow.ui.viewmodel  MutableStateFlow com.focusflow.ui.viewmodel  OnboardingStep com.focusflow.ui.viewmodel  OnboardingUiState com.focusflow.ui.viewmodel  OnboardingViewModel com.focusflow.ui.viewmodel  Pair com.focusflow.ui.viewmodel  PayoffCalculation com.focusflow.ui.viewmodel  
PayoffStep com.focusflow.ui.viewmodel  PayoffStrategy com.focusflow.ui.viewmodel  	StateFlow com.focusflow.ui.viewmodel  String com.focusflow.ui.viewmodel  System com.focusflow.ui.viewmodel  TimeZone com.focusflow.ui.viewmodel  Triple com.focusflow.ui.viewmodel  _uiState com.focusflow.ui.viewmodel  asStateFlow com.focusflow.ui.viewmodel  atTime com.focusflow.ui.viewmodel  budgetCategoryRepository com.focusflow.ui.viewmodel  
coerceAtLeast com.focusflow.ui.viewmodel  coerceAtMost com.focusflow.ui.viewmodel  com com.focusflow.ui.viewmodel  contains com.focusflow.ui.viewmodel  creditCardRepository com.focusflow.ui.viewmodel  delay com.focusflow.ui.viewmodel  	emptyList com.focusflow.ui.viewmodel  expenseRepository com.focusflow.ui.viewmodel  firstOrNull com.focusflow.ui.viewmodel  format com.focusflow.ui.viewmodel  gamificationService com.focusflow.ui.viewmodel  generateAIResponse com.focusflow.ui.viewmodel  getCurrentPeriodDates com.focusflow.ui.viewmodel  getCurrentPeriodValues com.focusflow.ui.viewmodel  invoke com.focusflow.ui.viewmodel  isBlank com.focusflow.ui.viewmodel  launch com.focusflow.ui.viewmodel  listOf com.focusflow.ui.viewmodel  loadCurrentPeriodExpenses com.focusflow.ui.viewmodel  loadDebtData com.focusflow.ui.viewmodel  	lowercase com.focusflow.ui.viewmodel  minus com.focusflow.ui.viewmodel  minusAssign com.focusflow.ui.viewmodel  plus com.focusflow.ui.viewmodel  
plusAssign com.focusflow.ui.viewmodel  random com.focusflow.ui.viewmodel  sumOf com.focusflow.ui.viewmodel  to com.focusflow.ui.viewmodel  toDoubleOrNull com.focusflow.ui.viewmodel  toLocalDateTime com.focusflow.ui.viewmodel  
trimIndent com.focusflow.ui.viewmodel  userPreferencesRepository com.focusflow.ui.viewmodel  viewModelScope com.focusflow.ui.viewmodel  Boolean )com.focusflow.ui.viewmodel.AICoachUiState  ChatMessage )com.focusflow.ui.viewmodel.AICoachUiState  List )com.focusflow.ui.viewmodel.AICoachUiState  String )com.focusflow.ui.viewmodel.AICoachUiState  
conversations )com.focusflow.ui.viewmodel.AICoachUiState  copy )com.focusflow.ui.viewmodel.AICoachUiState  currentMessage )com.focusflow.ui.viewmodel.AICoachUiState  	emptyList )com.focusflow.ui.viewmodel.AICoachUiState  error )com.focusflow.ui.viewmodel.AICoachUiState  	isLoading )com.focusflow.ui.viewmodel.AICoachUiState  AICoachUiState +com.focusflow.ui.viewmodel.AICoachViewModel  ChatMessage +com.focusflow.ui.viewmodel.AICoachViewModel  CreditCardRepository +com.focusflow.ui.viewmodel.AICoachViewModel  	Exception +com.focusflow.ui.viewmodel.AICoachViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.AICoachViewModel  Inject +com.focusflow.ui.viewmodel.AICoachViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  	StateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  String +com.focusflow.ui.viewmodel.AICoachViewModel  System +com.focusflow.ui.viewmodel.AICoachViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.AICoachViewModel  _uiState +com.focusflow.ui.viewmodel.AICoachViewModel  asStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  contains +com.focusflow.ui.viewmodel.AICoachViewModel  delay +com.focusflow.ui.viewmodel.AICoachViewModel  format +com.focusflow.ui.viewmodel.AICoachViewModel  generateAIResponse +com.focusflow.ui.viewmodel.AICoachViewModel  generateBudgetAdvice +com.focusflow.ui.viewmodel.AICoachViewModel  generateDebtAdvice +com.focusflow.ui.viewmodel.AICoachViewModel  generateGeneralResponse +com.focusflow.ui.viewmodel.AICoachViewModel  generateMoneyTip +com.focusflow.ui.viewmodel.AICoachViewModel  generateProgressReport +com.focusflow.ui.viewmodel.AICoachViewModel  generateSpendingAnalysis +com.focusflow.ui.viewmodel.AICoachViewModel  generateTaskBreakdown +com.focusflow.ui.viewmodel.AICoachViewModel  getASStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.AICoachViewModel  getCONTAINS +com.focusflow.ui.viewmodel.AICoachViewModel  getContains +com.focusflow.ui.viewmodel.AICoachViewModel  getDELAY +com.focusflow.ui.viewmodel.AICoachViewModel  getDelay +com.focusflow.ui.viewmodel.AICoachViewModel  	getFORMAT +com.focusflow.ui.viewmodel.AICoachViewModel  	getFormat +com.focusflow.ui.viewmodel.AICoachViewModel  
getISBlank +com.focusflow.ui.viewmodel.AICoachViewModel  
getIsBlank +com.focusflow.ui.viewmodel.AICoachViewModel  	getLAUNCH +com.focusflow.ui.viewmodel.AICoachViewModel  	getLISTOf +com.focusflow.ui.viewmodel.AICoachViewModel  getLOWERCASE +com.focusflow.ui.viewmodel.AICoachViewModel  	getLaunch +com.focusflow.ui.viewmodel.AICoachViewModel  	getListOf +com.focusflow.ui.viewmodel.AICoachViewModel  getLowercase +com.focusflow.ui.viewmodel.AICoachViewModel  getPLUS +com.focusflow.ui.viewmodel.AICoachViewModel  getPlus +com.focusflow.ui.viewmodel.AICoachViewModel  	getRANDOM +com.focusflow.ui.viewmodel.AICoachViewModel  	getRandom +com.focusflow.ui.viewmodel.AICoachViewModel  
getTRIMIndent +com.focusflow.ui.viewmodel.AICoachViewModel  
getTrimIndent +com.focusflow.ui.viewmodel.AICoachViewModel  getVIEWModelScope +com.focusflow.ui.viewmodel.AICoachViewModel  getViewModelScope +com.focusflow.ui.viewmodel.AICoachViewModel  isBlank +com.focusflow.ui.viewmodel.AICoachViewModel  launch +com.focusflow.ui.viewmodel.AICoachViewModel  listOf +com.focusflow.ui.viewmodel.AICoachViewModel  	lowercase +com.focusflow.ui.viewmodel.AICoachViewModel  plus +com.focusflow.ui.viewmodel.AICoachViewModel  random +com.focusflow.ui.viewmodel.AICoachViewModel  sendMessage +com.focusflow.ui.viewmodel.AICoachViewModel  
trimIndent +com.focusflow.ui.viewmodel.AICoachViewModel  uiState +com.focusflow.ui.viewmodel.AICoachViewModel  updateCurrentMessage +com.focusflow.ui.viewmodel.AICoachViewModel  userPreferencesRepository +com.focusflow.ui.viewmodel.AICoachViewModel  viewModelScope +com.focusflow.ui.viewmodel.AICoachViewModel  Boolean (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory (com.focusflow.ui.viewmodel.BudgetUiState  Double (com.focusflow.ui.viewmodel.BudgetUiState  List (com.focusflow.ui.viewmodel.BudgetUiState  String (com.focusflow.ui.viewmodel.BudgetUiState  budgetCategories (com.focusflow.ui.viewmodel.BudgetUiState  budgetPeriod (com.focusflow.ui.viewmodel.BudgetUiState  copy (com.focusflow.ui.viewmodel.BudgetUiState  	emptyList (com.focusflow.ui.viewmodel.BudgetUiState  error (com.focusflow.ui.viewmodel.BudgetUiState  totalBudget (com.focusflow.ui.viewmodel.BudgetUiState  
totalSpent (com.focusflow.ui.viewmodel.BudgetUiState  BudgetCategory *com.focusflow.ui.viewmodel.BudgetViewModel  BudgetCategoryRepository *com.focusflow.ui.viewmodel.BudgetViewModel  
BudgetUiState *com.focusflow.ui.viewmodel.BudgetViewModel  Clock *com.focusflow.ui.viewmodel.BudgetViewModel  Double *com.focusflow.ui.viewmodel.BudgetViewModel  	Exception *com.focusflow.ui.viewmodel.BudgetViewModel  ExpenseRepository *com.focusflow.ui.viewmodel.BudgetViewModel  Inject *com.focusflow.ui.viewmodel.BudgetViewModel  Int *com.focusflow.ui.viewmodel.BudgetViewModel  
LocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  MutableStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  	StateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  String *com.focusflow.ui.viewmodel.BudgetViewModel  TimeZone *com.focusflow.ui.viewmodel.BudgetViewModel  Triple *com.focusflow.ui.viewmodel.BudgetViewModel  UserPreferencesRepository *com.focusflow.ui.viewmodel.BudgetViewModel  _uiState *com.focusflow.ui.viewmodel.BudgetViewModel  addBudgetCategory *com.focusflow.ui.viewmodel.BudgetViewModel  asStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  budgetCategoryRepository *com.focusflow.ui.viewmodel.BudgetViewModel  
clearError *com.focusflow.ui.viewmodel.BudgetViewModel  deleteBudgetCategory *com.focusflow.ui.viewmodel.BudgetViewModel  getASStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getAsStateFlow *com.focusflow.ui.viewmodel.BudgetViewModel  getCurrentPeriodValues *com.focusflow.ui.viewmodel.BudgetViewModel  	getLAUNCH *com.focusflow.ui.viewmodel.BudgetViewModel  	getLaunch *com.focusflow.ui.viewmodel.BudgetViewModel  getSUMOf *com.focusflow.ui.viewmodel.BudgetViewModel  getSumOf *com.focusflow.ui.viewmodel.BudgetViewModel  getTOLocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  getToLocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  getVIEWModelScope *com.focusflow.ui.viewmodel.BudgetViewModel  getViewModelScope *com.focusflow.ui.viewmodel.BudgetViewModel  launch *com.focusflow.ui.viewmodel.BudgetViewModel  loadBudgetData *com.focusflow.ui.viewmodel.BudgetViewModel  sumOf *com.focusflow.ui.viewmodel.BudgetViewModel  toLocalDateTime *com.focusflow.ui.viewmodel.BudgetViewModel  uiState *com.focusflow.ui.viewmodel.BudgetViewModel  userPreferencesRepository *com.focusflow.ui.viewmodel.BudgetViewModel  viewModelScope *com.focusflow.ui.viewmodel.BudgetViewModel  Boolean &com.focusflow.ui.viewmodel.ChatMessage  Long &com.focusflow.ui.viewmodel.ChatMessage  String &com.focusflow.ui.viewmodel.ChatMessage  content &com.focusflow.ui.viewmodel.ChatMessage  
isFromUser &com.focusflow.ui.viewmodel.ChatMessage  Boolean +com.focusflow.ui.viewmodel.DashboardUiState  Double +com.focusflow.ui.viewmodel.DashboardUiState  String +com.focusflow.ui.viewmodel.DashboardUiState  budgetPeriod +com.focusflow.ui.viewmodel.DashboardUiState  copy +com.focusflow.ui.viewmodel.DashboardUiState  error +com.focusflow.ui.viewmodel.DashboardUiState  	isLoading +com.focusflow.ui.viewmodel.DashboardUiState  nextPayment +com.focusflow.ui.viewmodel.DashboardUiState  safeToSpend +com.focusflow.ui.viewmodel.DashboardUiState  	totalDebt +com.focusflow.ui.viewmodel.DashboardUiState  Clock -com.focusflow.ui.viewmodel.DashboardViewModel  CreditCardRepository -com.focusflow.ui.viewmodel.DashboardViewModel  DashboardUiState -com.focusflow.ui.viewmodel.DashboardViewModel  DateTimeUnit -com.focusflow.ui.viewmodel.DashboardViewModel  	Exception -com.focusflow.ui.viewmodel.DashboardViewModel  ExpenseRepository -com.focusflow.ui.viewmodel.DashboardViewModel  Inject -com.focusflow.ui.viewmodel.DashboardViewModel  	LocalDate -com.focusflow.ui.viewmodel.DashboardViewModel  
LocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  MutableStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  Pair -com.focusflow.ui.viewmodel.DashboardViewModel  	StateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  String -com.focusflow.ui.viewmodel.DashboardViewModel  TimeZone -com.focusflow.ui.viewmodel.DashboardViewModel  UserPreferencesRepository -com.focusflow.ui.viewmodel.DashboardViewModel  _uiState -com.focusflow.ui.viewmodel.DashboardViewModel  asStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  atTime -com.focusflow.ui.viewmodel.DashboardViewModel  creditCardRepository -com.focusflow.ui.viewmodel.DashboardViewModel  expenseRepository -com.focusflow.ui.viewmodel.DashboardViewModel  getASStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  	getATTime -com.focusflow.ui.viewmodel.DashboardViewModel  getAsStateFlow -com.focusflow.ui.viewmodel.DashboardViewModel  	getAtTime -com.focusflow.ui.viewmodel.DashboardViewModel  getCurrentPeriodDates -com.focusflow.ui.viewmodel.DashboardViewModel  	getLAUNCH -com.focusflow.ui.viewmodel.DashboardViewModel  	getLaunch -com.focusflow.ui.viewmodel.DashboardViewModel  getMINUS -com.focusflow.ui.viewmodel.DashboardViewModel  getMinus -com.focusflow.ui.viewmodel.DashboardViewModel  getPLUS -com.focusflow.ui.viewmodel.DashboardViewModel  getPlus -com.focusflow.ui.viewmodel.DashboardViewModel  getTOLocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  getToLocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  getVIEWModelScope -com.focusflow.ui.viewmodel.DashboardViewModel  getViewModelScope -com.focusflow.ui.viewmodel.DashboardViewModel  invoke -com.focusflow.ui.viewmodel.DashboardViewModel  launch -com.focusflow.ui.viewmodel.DashboardViewModel  loadDashboardData -com.focusflow.ui.viewmodel.DashboardViewModel  minus -com.focusflow.ui.viewmodel.DashboardViewModel  plus -com.focusflow.ui.viewmodel.DashboardViewModel  toLocalDateTime -com.focusflow.ui.viewmodel.DashboardViewModel  uiState -com.focusflow.ui.viewmodel.DashboardViewModel  userPreferencesRepository -com.focusflow.ui.viewmodel.DashboardViewModel  viewModelScope -com.focusflow.ui.viewmodel.DashboardViewModel  Boolean &com.focusflow.ui.viewmodel.DebtUiState  Double &com.focusflow.ui.viewmodel.DebtUiState  String &com.focusflow.ui.viewmodel.DebtUiState  copy &com.focusflow.ui.viewmodel.DebtUiState  error &com.focusflow.ui.viewmodel.DebtUiState  	totalDebt &com.focusflow.ui.viewmodel.DebtUiState  totalMinimumPayments &com.focusflow.ui.viewmodel.DebtUiState  Clock (com.focusflow.ui.viewmodel.DebtViewModel  
CreditCard (com.focusflow.ui.viewmodel.DebtViewModel  CreditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  DateTimeUnit (com.focusflow.ui.viewmodel.DebtViewModel  DebtUiState (com.focusflow.ui.viewmodel.DebtViewModel  Double (com.focusflow.ui.viewmodel.DebtViewModel  	Exception (com.focusflow.ui.viewmodel.DebtViewModel  Inject (com.focusflow.ui.viewmodel.DebtViewModel  List (com.focusflow.ui.viewmodel.DebtViewModel  	LocalDate (com.focusflow.ui.viewmodel.DebtViewModel  Long (com.focusflow.ui.viewmodel.DebtViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  PayoffCalculation (com.focusflow.ui.viewmodel.DebtViewModel  
PayoffStep (com.focusflow.ui.viewmodel.DebtViewModel  PayoffStrategy (com.focusflow.ui.viewmodel.DebtViewModel  	StateFlow (com.focusflow.ui.viewmodel.DebtViewModel  String (com.focusflow.ui.viewmodel.DebtViewModel  TimeZone (com.focusflow.ui.viewmodel.DebtViewModel  _uiState (com.focusflow.ui.viewmodel.DebtViewModel  
addCreditCard (com.focusflow.ui.viewmodel.DebtViewModel  allCreditCards (com.focusflow.ui.viewmodel.DebtViewModel  asStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  
clearError (com.focusflow.ui.viewmodel.DebtViewModel  
coerceAtLeast (com.focusflow.ui.viewmodel.DebtViewModel  coerceAtMost (com.focusflow.ui.viewmodel.DebtViewModel  creditCardRepository (com.focusflow.ui.viewmodel.DebtViewModel  deleteCreditCard (com.focusflow.ui.viewmodel.DebtViewModel  generatePayoffPlan (com.focusflow.ui.viewmodel.DebtViewModel  getASStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.DebtViewModel  getCOERCEAtLeast (com.focusflow.ui.viewmodel.DebtViewModel  getCOERCEAtMost (com.focusflow.ui.viewmodel.DebtViewModel  getCoerceAtLeast (com.focusflow.ui.viewmodel.DebtViewModel  getCoerceAtMost (com.focusflow.ui.viewmodel.DebtViewModel  	getLAUNCH (com.focusflow.ui.viewmodel.DebtViewModel  	getLISTOf (com.focusflow.ui.viewmodel.DebtViewModel  	getLaunch (com.focusflow.ui.viewmodel.DebtViewModel  	getListOf (com.focusflow.ui.viewmodel.DebtViewModel  getMINUSAssign (com.focusflow.ui.viewmodel.DebtViewModel  getMinusAssign (com.focusflow.ui.viewmodel.DebtViewModel  getPLUS (com.focusflow.ui.viewmodel.DebtViewModel  
getPLUSAssign (com.focusflow.ui.viewmodel.DebtViewModel  getPlus (com.focusflow.ui.viewmodel.DebtViewModel  
getPlusAssign (com.focusflow.ui.viewmodel.DebtViewModel  getTOLocalDateTime (com.focusflow.ui.viewmodel.DebtViewModel  getToLocalDateTime (com.focusflow.ui.viewmodel.DebtViewModel  getVIEWModelScope (com.focusflow.ui.viewmodel.DebtViewModel  getViewModelScope (com.focusflow.ui.viewmodel.DebtViewModel  launch (com.focusflow.ui.viewmodel.DebtViewModel  listOf (com.focusflow.ui.viewmodel.DebtViewModel  loadDebtData (com.focusflow.ui.viewmodel.DebtViewModel  makePayment (com.focusflow.ui.viewmodel.DebtViewModel  minusAssign (com.focusflow.ui.viewmodel.DebtViewModel  plus (com.focusflow.ui.viewmodel.DebtViewModel  
plusAssign (com.focusflow.ui.viewmodel.DebtViewModel  toLocalDateTime (com.focusflow.ui.viewmodel.DebtViewModel  uiState (com.focusflow.ui.viewmodel.DebtViewModel  viewModelScope (com.focusflow.ui.viewmodel.DebtViewModel  
categories 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  	getLISTOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  	getListOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTO 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  getTo 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  listOf 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  to 2com.focusflow.ui.viewmodel.DefaultBudgetCategories  
categories ,com.focusflow.ui.viewmodel.ExpenseCategories  	getLISTOf ,com.focusflow.ui.viewmodel.ExpenseCategories  	getListOf ,com.focusflow.ui.viewmodel.ExpenseCategories  listOf ,com.focusflow.ui.viewmodel.ExpenseCategories  Boolean )com.focusflow.ui.viewmodel.ExpenseUiState  Double )com.focusflow.ui.viewmodel.ExpenseUiState  Expense )com.focusflow.ui.viewmodel.ExpenseUiState  List )com.focusflow.ui.viewmodel.ExpenseUiState  String )com.focusflow.ui.viewmodel.ExpenseUiState  budgetPeriod )com.focusflow.ui.viewmodel.ExpenseUiState  copy )com.focusflow.ui.viewmodel.ExpenseUiState  currentPeriodExpenses )com.focusflow.ui.viewmodel.ExpenseUiState  	emptyList )com.focusflow.ui.viewmodel.ExpenseUiState  error )com.focusflow.ui.viewmodel.ExpenseUiState  totalSpentThisPeriod )com.focusflow.ui.viewmodel.ExpenseUiState  Clock +com.focusflow.ui.viewmodel.ExpenseViewModel  DateTimeUnit +com.focusflow.ui.viewmodel.ExpenseViewModel  Double +com.focusflow.ui.viewmodel.ExpenseViewModel  	Exception +com.focusflow.ui.viewmodel.ExpenseViewModel  Expense +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  ExpenseUiState +com.focusflow.ui.viewmodel.ExpenseViewModel  Flow +com.focusflow.ui.viewmodel.ExpenseViewModel  Inject +com.focusflow.ui.viewmodel.ExpenseViewModel  List +com.focusflow.ui.viewmodel.ExpenseViewModel  	LocalDate +com.focusflow.ui.viewmodel.ExpenseViewModel  
LocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  MutableStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  Pair +com.focusflow.ui.viewmodel.ExpenseViewModel  	StateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  String +com.focusflow.ui.viewmodel.ExpenseViewModel  TimeZone +com.focusflow.ui.viewmodel.ExpenseViewModel  UserPreferencesRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  _uiState +com.focusflow.ui.viewmodel.ExpenseViewModel  
addExpense +com.focusflow.ui.viewmodel.ExpenseViewModel  allExpenses +com.focusflow.ui.viewmodel.ExpenseViewModel  asStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  atTime +com.focusflow.ui.viewmodel.ExpenseViewModel  
clearError +com.focusflow.ui.viewmodel.ExpenseViewModel  com +com.focusflow.ui.viewmodel.ExpenseViewModel  
deleteExpense +com.focusflow.ui.viewmodel.ExpenseViewModel  expenseRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  gamificationService +com.focusflow.ui.viewmodel.ExpenseViewModel  getASStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  	getATTime +com.focusflow.ui.viewmodel.ExpenseViewModel  getAsStateFlow +com.focusflow.ui.viewmodel.ExpenseViewModel  	getAtTime +com.focusflow.ui.viewmodel.ExpenseViewModel  getCurrentPeriodDates +com.focusflow.ui.viewmodel.ExpenseViewModel  	getLAUNCH +com.focusflow.ui.viewmodel.ExpenseViewModel  	getLaunch +com.focusflow.ui.viewmodel.ExpenseViewModel  getMINUS +com.focusflow.ui.viewmodel.ExpenseViewModel  getMinus +com.focusflow.ui.viewmodel.ExpenseViewModel  getPLUS +com.focusflow.ui.viewmodel.ExpenseViewModel  getPlus +com.focusflow.ui.viewmodel.ExpenseViewModel  getTOLocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  getToLocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  getVIEWModelScope +com.focusflow.ui.viewmodel.ExpenseViewModel  getViewModelScope +com.focusflow.ui.viewmodel.ExpenseViewModel  invoke +com.focusflow.ui.viewmodel.ExpenseViewModel  launch +com.focusflow.ui.viewmodel.ExpenseViewModel  loadCurrentPeriodExpenses +com.focusflow.ui.viewmodel.ExpenseViewModel  minus +com.focusflow.ui.viewmodel.ExpenseViewModel  plus +com.focusflow.ui.viewmodel.ExpenseViewModel  toLocalDateTime +com.focusflow.ui.viewmodel.ExpenseViewModel  uiState +com.focusflow.ui.viewmodel.ExpenseViewModel  userPreferencesRepository +com.focusflow.ui.viewmodel.ExpenseViewModel  viewModelScope +com.focusflow.ui.viewmodel.ExpenseViewModel  Boolean &com.focusflow.ui.viewmodel.MainUiState  String &com.focusflow.ui.viewmodel.MainUiState  copy &com.focusflow.ui.viewmodel.MainUiState  hasCompletedOnboarding &com.focusflow.ui.viewmodel.MainUiState  	isLoading &com.focusflow.ui.viewmodel.MainUiState  	Exception (com.focusflow.ui.viewmodel.MainViewModel  Inject (com.focusflow.ui.viewmodel.MainViewModel  MainUiState (com.focusflow.ui.viewmodel.MainViewModel  MutableStateFlow (com.focusflow.ui.viewmodel.MainViewModel  	StateFlow (com.focusflow.ui.viewmodel.MainViewModel  UserPreferencesRepository (com.focusflow.ui.viewmodel.MainViewModel  _uiState (com.focusflow.ui.viewmodel.MainViewModel  asStateFlow (com.focusflow.ui.viewmodel.MainViewModel  checkOnboardingStatus (com.focusflow.ui.viewmodel.MainViewModel  com (com.focusflow.ui.viewmodel.MainViewModel  gamificationService (com.focusflow.ui.viewmodel.MainViewModel  getASStateFlow (com.focusflow.ui.viewmodel.MainViewModel  getAsStateFlow (com.focusflow.ui.viewmodel.MainViewModel  	getLAUNCH (com.focusflow.ui.viewmodel.MainViewModel  	getLaunch (com.focusflow.ui.viewmodel.MainViewModel  getVIEWModelScope (com.focusflow.ui.viewmodel.MainViewModel  getViewModelScope (com.focusflow.ui.viewmodel.MainViewModel  launch (com.focusflow.ui.viewmodel.MainViewModel  uiState (com.focusflow.ui.viewmodel.MainViewModel  userPreferencesRepository (com.focusflow.ui.viewmodel.MainViewModel  viewModelScope (com.focusflow.ui.viewmodel.MainViewModel  
ADHD_FRIENDLY )com.focusflow.ui.viewmodel.OnboardingStep  BUDGET_SETUP )com.focusflow.ui.viewmodel.OnboardingStep  COMPLETE )com.focusflow.ui.viewmodel.OnboardingStep  
DEBT_SETUP )com.focusflow.ui.viewmodel.OnboardingStep  GOALS_FINANCIAL )com.focusflow.ui.viewmodel.OnboardingStep  GOALS_PERSONAL )com.focusflow.ui.viewmodel.OnboardingStep  INCOME_SETUP )com.focusflow.ui.viewmodel.OnboardingStep  NOTIFICATION_SETUP )com.focusflow.ui.viewmodel.OnboardingStep  WELCOME )com.focusflow.ui.viewmodel.OnboardingStep  ordinal )com.focusflow.ui.viewmodel.OnboardingStep  values )com.focusflow.ui.viewmodel.OnboardingStep  Boolean ,com.focusflow.ui.viewmodel.OnboardingUiState  List ,com.focusflow.ui.viewmodel.OnboardingUiState  OnboardingStep ,com.focusflow.ui.viewmodel.OnboardingUiState  String ,com.focusflow.ui.viewmodel.OnboardingUiState  copy ,com.focusflow.ui.viewmodel.OnboardingUiState  currentStep ,com.focusflow.ui.viewmodel.OnboardingUiState  	emptyList ,com.focusflow.ui.viewmodel.OnboardingUiState  enableNotifications ,com.focusflow.ui.viewmodel.OnboardingUiState  hasDebt ,com.focusflow.ui.viewmodel.OnboardingUiState  
monthlyIncome ,com.focusflow.ui.viewmodel.OnboardingUiState  notificationTime ,com.focusflow.ui.viewmodel.OnboardingUiState  selectedFinancialGoals ,com.focusflow.ui.viewmodel.OnboardingUiState  selectedPersonalGoals ,com.focusflow.ui.viewmodel.OnboardingUiState  weeklyBudget ,com.focusflow.ui.viewmodel.OnboardingUiState  Boolean .com.focusflow.ui.viewmodel.OnboardingViewModel  Double .com.focusflow.ui.viewmodel.OnboardingViewModel  	Exception .com.focusflow.ui.viewmodel.OnboardingViewModel  Inject .com.focusflow.ui.viewmodel.OnboardingViewModel  List .com.focusflow.ui.viewmodel.OnboardingViewModel  MutableStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  OnboardingStep .com.focusflow.ui.viewmodel.OnboardingViewModel  OnboardingUiState .com.focusflow.ui.viewmodel.OnboardingViewModel  	StateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  String .com.focusflow.ui.viewmodel.OnboardingViewModel  UserPreferencesRepository .com.focusflow.ui.viewmodel.OnboardingViewModel  _uiState .com.focusflow.ui.viewmodel.OnboardingViewModel  asStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  com .com.focusflow.ui.viewmodel.OnboardingViewModel  completeOnboarding .com.focusflow.ui.viewmodel.OnboardingViewModel  firstOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  getASStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getAsStateFlow .com.focusflow.ui.viewmodel.OnboardingViewModel  getCOM .com.focusflow.ui.viewmodel.OnboardingViewModel  getCom .com.focusflow.ui.viewmodel.OnboardingViewModel  getFIRSTOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  getFirstOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  	getLAUNCH .com.focusflow.ui.viewmodel.OnboardingViewModel  	getLaunch .com.focusflow.ui.viewmodel.OnboardingViewModel  getTODoubleOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  getToDoubleOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  getVIEWModelScope .com.focusflow.ui.viewmodel.OnboardingViewModel  getViewModelScope .com.focusflow.ui.viewmodel.OnboardingViewModel  launch .com.focusflow.ui.viewmodel.OnboardingViewModel  nextStep .com.focusflow.ui.viewmodel.OnboardingViewModel  toDoubleOrNull .com.focusflow.ui.viewmodel.OnboardingViewModel  uiState .com.focusflow.ui.viewmodel.OnboardingViewModel  updateFinancialGoals .com.focusflow.ui.viewmodel.OnboardingViewModel  
updateHasDebt .com.focusflow.ui.viewmodel.OnboardingViewModel  updateMonthlyIncome .com.focusflow.ui.viewmodel.OnboardingViewModel  updateNotificationSettings .com.focusflow.ui.viewmodel.OnboardingViewModel  updatePersonalGoals .com.focusflow.ui.viewmodel.OnboardingViewModel  updateWeeklyBudget .com.focusflow.ui.viewmodel.OnboardingViewModel  userPreferencesRepository .com.focusflow.ui.viewmodel.OnboardingViewModel  viewModelScope .com.focusflow.ui.viewmodel.OnboardingViewModel  Double ,com.focusflow.ui.viewmodel.PayoffCalculation  Int ,com.focusflow.ui.viewmodel.PayoffCalculation  Double %com.focusflow.ui.viewmodel.PayoffStep  Int %com.focusflow.ui.viewmodel.PayoffStep  String %com.focusflow.ui.viewmodel.PayoffStep  	AVALANCHE )com.focusflow.ui.viewmodel.PayoffStrategy  SNOWBALL )com.focusflow.ui.viewmodel.PayoffStrategy  equals )com.focusflow.ui.viewmodel.PayoffStrategy  AccessibilityUtils com.focusflow.utils  Boolean com.focusflow.utils  DataIntegrityUtils com.focusflow.utils  Double com.focusflow.utils  ErrorHandler com.focusflow.utils  
ErrorSnackbar com.focusflow.utils  IllegalArgumentException com.focusflow.utils  List com.focusflow.utils  Log com.focusflow.utils  Long com.focusflow.utils  NumberFormatException com.focusflow.utils  PerformanceMonitor com.focusflow.utils  Regex com.focusflow.utils  SecurityException com.focusflow.utils  
SecurityUtils com.focusflow.utils  SnackbarDuration com.focusflow.utils  String com.focusflow.utils  System com.focusflow.utils  	Throwable com.focusflow.utils  Unit com.focusflow.utils  ValidationUtils com.focusflow.utils  all com.focusflow.utils  android com.focusflow.utils  androidx com.focusflow.utils  com com.focusflow.utils  contains com.focusflow.utils  forEach com.focusflow.utils  format com.focusflow.utils  invoke com.focusflow.utils  isDigit com.focusflow.utils  java com.focusflow.utils  let com.focusflow.utils  listOf com.focusflow.utils  	lowercase com.focusflow.utils  
mutableListOf com.focusflow.utils  mutableMapOf com.focusflow.utils  none com.focusflow.utils  replace com.focusflow.utils  set com.focusflow.utils  take com.focusflow.utils  toDouble com.focusflow.utils  toRegex com.focusflow.utils  trim com.focusflow.utils  Double &com.focusflow.utils.AccessibilityUtils  String &com.focusflow.utils.AccessibilityUtils  androidx &com.focusflow.utils.AccessibilityUtils  format &com.focusflow.utils.AccessibilityUtils  getANDROIDX &com.focusflow.utils.AccessibilityUtils  getAndroidx &com.focusflow.utils.AccessibilityUtils  	getFORMAT &com.focusflow.utils.AccessibilityUtils  	getFormat &com.focusflow.utils.AccessibilityUtils  List &com.focusflow.utils.DataIntegrityUtils  String &com.focusflow.utils.DataIntegrityUtils  com &com.focusflow.utils.DataIntegrityUtils  getMUTABLEListOf &com.focusflow.utils.DataIntegrityUtils  getMutableListOf &com.focusflow.utils.DataIntegrityUtils  
mutableListOf &com.focusflow.utils.DataIntegrityUtils  IllegalArgumentException  com.focusflow.utils.ErrorHandler  Log  com.focusflow.utils.ErrorHandler  SecurityException  com.focusflow.utils.ErrorHandler  SnackbarDuration  com.focusflow.utils.ErrorHandler  SnackbarHostState  com.focusflow.utils.ErrorHandler  String  com.focusflow.utils.ErrorHandler  TAG  com.focusflow.utils.ErrorHandler  	Throwable  com.focusflow.utils.ErrorHandler  java  com.focusflow.utils.ErrorHandler  
logWarning  com.focusflow.utils.ErrorHandler  showErrorSnackbar  com.focusflow.utils.ErrorHandler  ErrorHandler &com.focusflow.utils.PerformanceMonitor  Long &com.focusflow.utils.PerformanceMonitor  String &com.focusflow.utils.PerformanceMonitor  System &com.focusflow.utils.PerformanceMonitor  endTimer &com.focusflow.utils.PerformanceMonitor  getMUTABLEMapOf &com.focusflow.utils.PerformanceMonitor  getMutableMapOf &com.focusflow.utils.PerformanceMonitor  getSET &com.focusflow.utils.PerformanceMonitor  getSet &com.focusflow.utils.PerformanceMonitor  mutableMapOf &com.focusflow.utils.PerformanceMonitor  performanceMetrics &com.focusflow.utils.PerformanceMonitor  set &com.focusflow.utils.PerformanceMonitor  
startTimer &com.focusflow.utils.PerformanceMonitor  Boolean !com.focusflow.utils.SecurityUtils  Regex !com.focusflow.utils.SecurityUtils  String !com.focusflow.utils.SecurityUtils  contains !com.focusflow.utils.SecurityUtils  getCONTAINS !com.focusflow.utils.SecurityUtils  getContains !com.focusflow.utils.SecurityUtils  	getLISTOf !com.focusflow.utils.SecurityUtils  getLOWERCASE !com.focusflow.utils.SecurityUtils  	getListOf !com.focusflow.utils.SecurityUtils  getLowercase !com.focusflow.utils.SecurityUtils  getNONE !com.focusflow.utils.SecurityUtils  getNone !com.focusflow.utils.SecurityUtils  
getREPLACE !com.focusflow.utils.SecurityUtils  
getReplace !com.focusflow.utils.SecurityUtils  invoke !com.focusflow.utils.SecurityUtils  listOf !com.focusflow.utils.SecurityUtils  	lowercase !com.focusflow.utils.SecurityUtils  none !com.focusflow.utils.SecurityUtils  replace !com.focusflow.utils.SecurityUtils  Boolean #com.focusflow.utils.ValidationUtils  Double #com.focusflow.utils.ValidationUtils  NumberFormatException #com.focusflow.utils.ValidationUtils  String #com.focusflow.utils.ValidationUtils  all #com.focusflow.utils.ValidationUtils  android #com.focusflow.utils.ValidationUtils  getALL #com.focusflow.utils.ValidationUtils  
getANDROID #com.focusflow.utils.ValidationUtils  getAll #com.focusflow.utils.ValidationUtils  
getAndroid #com.focusflow.utils.ValidationUtils  
getISDigit #com.focusflow.utils.ValidationUtils  
getIsDigit #com.focusflow.utils.ValidationUtils  
getREPLACE #com.focusflow.utils.ValidationUtils  
getReplace #com.focusflow.utils.ValidationUtils  getTAKE #com.focusflow.utils.ValidationUtils  getTODouble #com.focusflow.utils.ValidationUtils  
getTORegex #com.focusflow.utils.ValidationUtils  getTRIM #com.focusflow.utils.ValidationUtils  getTake #com.focusflow.utils.ValidationUtils  getToDouble #com.focusflow.utils.ValidationUtils  
getToRegex #com.focusflow.utils.ValidationUtils  getTrim #com.focusflow.utils.ValidationUtils  isDigit #com.focusflow.utils.ValidationUtils  replace #com.focusflow.utils.ValidationUtils  take #com.focusflow.utils.ValidationUtils  toDouble #com.focusflow.utils.ValidationUtils  toRegex #com.focusflow.utils.ValidationUtils  trim #com.focusflow.utils.ValidationUtils  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  IOException java.io  ADHDFriendlyStep 	java.lang  
AICoachScreen 	java.lang  AICoachUiState 	java.lang  
AIInteraction 	java.lang  Achievement 	java.lang  AchievementBadge 	java.lang  	Alignment 	java.lang  Arrangement 	java.lang  Box 	java.lang  BudgetAmountItem 	java.lang  BudgetCategory 	java.lang  BudgetCategoryItem 	java.lang  BudgetOverviewCard 	java.lang  BudgetScreen 	java.lang  BudgetSetupStep 	java.lang  
BudgetUiState 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  
CHANNEL_ID 	java.lang  Card 	java.lang  ChatMessage 	java.lang  Checkbox 	java.lang  CircleShape 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Clock 	java.lang  Color 	java.lang  Column 	java.lang  CompleteStep 	java.lang  CompositionLocalProvider 	java.lang  Context 	java.lang  
Converters 	java.lang  
CreditCard 	java.lang  CreditCardItem 	java.lang  CreditCardSummaryCard 	java.lang  DashboardScreen 	java.lang  DashboardUiState 	java.lang  DateTimeUnit 	java.lang  DebtOverviewCard 	java.lang  
DebtScreen 	java.lang  
DebtSetupStep 	java.lang  DebtUiState 	java.lang  Double 	java.lang  EmptyBudgetState 	java.lang  EmptyStateCard 	java.lang  ErrorHandler 	java.lang  	Exception 	java.lang  Expense 	java.lang  ExpenseCategories 	java.lang  ExpenseItem 	java.lang  ExpenseUiState 	java.lang  ExpensesScreen 	java.lang  
FilterChip 	java.lang  FinancialGoalsStep 	java.lang  FloatingActionButton 	java.lang  FocusFlowApp 	java.lang  FocusFlowDatabase 	java.lang  FocusFlowTheme 	java.lang  
FontWeight 	java.lang  	GridCells 	java.lang  HabitLog 	java.lang  HabitStreakCard 	java.lang  HabitStreakItem 	java.lang  HabitsScreen 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  ImpulseControlQuestions 	java.lang  IncomeSetupStep 	java.lang  Intent 	java.lang  KeyboardOptions 	java.lang  KeyboardType 	java.lang  
LazyColumn 	java.lang  LazyRow 	java.lang  LazyVerticalGrid 	java.lang  LinearProgressIndicator 	java.lang  LocalContentColor 	java.lang  LocalContext 	java.lang  	LocalDate 	java.lang  
LocalDateTime 	java.lang  Log 	java.lang  MainActivity 	java.lang  MainUiState 	java.lang  
MaterialTheme 	java.lang  
MessageBubble 	java.lang  MessageInputField 	java.lang  Modifier 	java.lang  MotivationalQuoteCard 	java.lang  MutableStateFlow 	java.lang  NOTIFICATION_ID 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  NotificationService 	java.lang  NotificationSetupStep 	java.lang  OnConflictStrategy 	java.lang  OnboardingProgressIndicator 	java.lang  OnboardingStep 	java.lang  OnboardingUiState 	java.lang  OutlinedButton 	java.lang  OutlinedTextField 	java.lang  Pair 	java.lang  PayoffCalculation 	java.lang  
PayoffStep 	java.lang  PayoffStrategy 	java.lang  
PendingIntent 	java.lang  PersonalGoalsStep 	java.lang  ProgressAchievementsCard 	java.lang  QuickCategoryOverview 	java.lang  RadioButton 	java.lang  Regex 	java.lang  
RepeatMode 	java.lang  Room 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  START_NOT_STICKY 	java.lang  SafeToSpendWidget 	java.lang  Screen 	java.lang  SecurityException 	java.lang  SingletonComponent 	java.lang  SnackbarDuration 	java.lang  Spacer 	java.lang  SpendingSummaryCard 	java.lang  String 	java.lang  StringListConverter 	java.lang  SuggestedPromptCard 	java.lang  SuggestedPromptsSection 	java.lang  Surface 	java.lang  Switch 	java.lang  System 	java.lang  Task 	java.lang  TaskItem 	java.lang  TasksScreen 	java.lang  Text 	java.lang  	TextAlign 	java.lang  
TextButton 	java.lang  TimeZone 	java.lang  TodaysTasksCard 	java.lang  Triple 	java.lang  TypingIndicator 	java.lang  UserPreferences 	java.lang  	UserStats 	java.lang  
VirtualPet 	java.lang  VirtualPetWidget 	java.lang  WelcomeStep 	java.lang  _uiState 	java.lang  all 	java.lang  android 	java.lang  androidx 	java.lang  animateFloatAsState 	java.lang  apply 	java.lang  asStateFlow 	java.lang  atTime 	java.lang  
background 	java.lang  bottomNavItems 	java.lang  budgetCategoryRepository 	java.lang  	clickable 	java.lang  
coerceAtLeast 	java.lang  coerceAtMost 	java.lang  coerceIn 	java.lang  com 	java.lang  
component1 	java.lang  
component2 	java.lang  contains 	java.lang  createNotificationChannel 	java.lang  creditCardRepository 	java.lang  currentBackStackEntryAsState 	java.lang  delay 	java.lang  	emptyList 	java.lang  expenseRepository 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  finish 	java.lang  firstOrNull 	java.lang  forEach 	java.lang  format 	java.lang  
formatDate 	java.lang  gamificationService 	java.lang  generateAIResponse 	java.lang  getCurrentPeriodDates 	java.lang  getCurrentPeriodValues 	java.lang  getValue 	java.lang  groupBy 	java.lang  height 	java.lang  infiniteRepeatable 	java.lang  invoke 	java.lang  isBlank 	java.lang  isDigit 	java.lang  isEmpty 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  joinToString 	java.lang  kotlinx 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  loadCurrentPeriodExpenses 	java.lang  loadDebtData 	java.lang  	lowercase 	java.lang  	mapValues 	java.lang  minOf 	java.lang  minus 	java.lang  minusAssign 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  mutableStateOf 	java.lang  none 	java.lang  padding 	java.lang  plus 	java.lang  
plusAssign 	java.lang  provideDelegate 	java.lang  random 	java.lang  remember 	java.lang  repeat 	java.lang  replace 	java.lang  replaceFirstChar 	java.lang  set 	java.lang  setValue 	java.lang  size 	java.lang  sortedByDescending 	java.lang  split 	java.lang  sumOf 	java.lang  synchronized 	java.lang  take 	java.lang  takeIf 	java.lang  to 	java.lang  toDouble 	java.lang  toDoubleOrNull 	java.lang  toIntOrNull 	java.lang  toList 	java.lang  toLocalDateTime 	java.lang  toMap 	java.lang  toMutableMap 	java.lang  toRegex 	java.lang  trim 	java.lang  
trimIndent 	java.lang  tween 	java.lang  userPreferencesRepository 	java.lang  width 	java.lang  widthIn 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  SocketTimeoutException java.net  UnknownHostException java.net  SimpleDateFormat 	java.text  Month 	java.time  ordinal java.time.DayOfWeek  Add 	java.util  AlertDialog 	java.util  	Alignment 	java.util  Arrangement 	java.util  Box 	java.util  Button 	java.util  ButtonDefaults 	java.util  Card 	java.util  CircleShape 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  CompositionLocalProvider 	java.util  CreditCardItem 	java.util  DebtOverviewCard 	java.util  Delete 	java.util  Edit 	java.util  EmptyStateCard 	java.util  ExpenseCategories 	java.util  ExpenseItem 	java.util  
FilterChip 	java.util  FloatingActionButton 	java.util  
FontWeight 	java.util  	GridCells 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  KeyboardArrowRight 	java.util  KeyboardArrowUp 	java.util  KeyboardOptions 	java.util  KeyboardType 	java.util  LaunchedEffect 	java.util  
LazyColumn 	java.util  LazyRow 	java.util  LazyVerticalGrid 	java.util  LinearProgressIndicator 	java.util  LocalContentColor 	java.util  
MaterialTheme 	java.util  
MessageBubble 	java.util  MessageInputField 	java.util  Modifier 	java.util  OutlinedTextField 	java.util  PayoffStrategy 	java.util  Person 	java.util  QuickCategoryOverview 	java.util  RadioButton 	java.util  
RepeatMode 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Send 	java.util  Spacer 	java.util  SpendingSummaryCard 	java.util  Star 	java.util  String 	java.util  SuggestedPromptCard 	java.util  SuggestedPromptsSection 	java.util  Surface 	java.util  System 	java.util  Text 	java.util  	TextAlign 	java.util  
TextButton 	java.util  TypingIndicator 	java.util  animateFloatAsState 	java.util  
background 	java.util  	clickable 	java.util  coerceIn 	java.util  
component1 	java.util  
component2 	java.util  	emptyList 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  forEach 	java.util  format 	java.util  
formatDate 	java.util  getValue 	java.util  groupBy 	java.util  height 	java.util  infiniteRepeatable 	java.util  
isNotBlank 	java.util  
isNotEmpty 	java.util  items 	java.util  let 	java.util  listOf 	java.util  	lowercase 	java.util  	mapValues 	java.util  mutableStateOf 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  repeat 	java.util  replaceFirstChar 	java.util  setValue 	java.util  size 	java.util  sortedByDescending 	java.util  sumOf 	java.util  take 	java.util  takeIf 	java.util  toDoubleOrNull 	java.util  toIntOrNull 	java.util  toList 	java.util  tween 	java.util  width 	java.util  widthIn 	java.util  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  Inject javax.inject  	Singleton javax.inject  ADHDFriendlyStep kotlin  
AICoachScreen kotlin  AICoachUiState kotlin  
AIInteraction kotlin  Achievement kotlin  AchievementBadge kotlin  	Alignment kotlin  Any kotlin  Arrangement kotlin  Array kotlin  Boolean kotlin  Box kotlin  BudgetAmountItem kotlin  BudgetCategory kotlin  BudgetCategoryItem kotlin  BudgetOverviewCard kotlin  BudgetScreen kotlin  BudgetSetupStep kotlin  
BudgetUiState kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  
CHANNEL_ID kotlin  Card kotlin  Char kotlin  ChatMessage kotlin  Checkbox kotlin  CircleShape kotlin  CircularProgressIndicator kotlin  Clock kotlin  Color kotlin  Column kotlin  CompleteStep kotlin  CompositionLocalProvider kotlin  Context kotlin  
Converters kotlin  
CreditCard kotlin  CreditCardItem kotlin  CreditCardSummaryCard kotlin  DashboardScreen kotlin  DashboardUiState kotlin  DateTimeUnit kotlin  DebtOverviewCard kotlin  
DebtScreen kotlin  
DebtSetupStep kotlin  DebtUiState kotlin  Double kotlin  EmptyBudgetState kotlin  EmptyStateCard kotlin  ErrorHandler kotlin  	Exception kotlin  Expense kotlin  ExpenseCategories kotlin  ExpenseItem kotlin  ExpenseUiState kotlin  ExpensesScreen kotlin  
FilterChip kotlin  FinancialGoalsStep kotlin  Float kotlin  FloatingActionButton kotlin  FocusFlowApp kotlin  FocusFlowDatabase kotlin  FocusFlowTheme kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function4 kotlin  	Function6 kotlin  	GridCells kotlin  HabitLog kotlin  HabitStreakCard kotlin  HabitStreakItem kotlin  HabitsScreen kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  IllegalArgumentException kotlin  ImpulseControlQuestions kotlin  IncomeSetupStep kotlin  Int kotlin  Intent kotlin  KeyboardOptions kotlin  KeyboardType kotlin  
LazyColumn kotlin  LazyRow kotlin  LazyVerticalGrid kotlin  LinearProgressIndicator kotlin  LocalContentColor kotlin  LocalContext kotlin  	LocalDate kotlin  
LocalDateTime kotlin  Log kotlin  Long kotlin  MainActivity kotlin  MainUiState kotlin  
MaterialTheme kotlin  
MessageBubble kotlin  MessageInputField kotlin  Modifier kotlin  MotivationalQuoteCard kotlin  MutableStateFlow kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  NotificationService kotlin  NotificationSetupStep kotlin  NumberFormatException kotlin  OnConflictStrategy kotlin  OnboardingProgressIndicator kotlin  OnboardingStep kotlin  OnboardingUiState kotlin  OutlinedButton kotlin  OutlinedTextField kotlin  Pair kotlin  PayoffCalculation kotlin  
PayoffStep kotlin  PayoffStrategy kotlin  
PendingIntent kotlin  PersonalGoalsStep kotlin  ProgressAchievementsCard kotlin  QuickCategoryOverview kotlin  RadioButton kotlin  Regex kotlin  
RepeatMode kotlin  Room kotlin  RoundedCornerShape kotlin  Row kotlin  START_NOT_STICKY kotlin  SafeToSpendWidget kotlin  Screen kotlin  SecurityException kotlin  SingletonComponent kotlin  SnackbarDuration kotlin  Spacer kotlin  SpendingSummaryCard kotlin  String kotlin  StringListConverter kotlin  SuggestedPromptCard kotlin  SuggestedPromptsSection kotlin  Surface kotlin  Switch kotlin  System kotlin  Task kotlin  TaskItem kotlin  TasksScreen kotlin  Text kotlin  	TextAlign kotlin  
TextButton kotlin  	Throwable kotlin  TimeZone kotlin  TodaysTasksCard kotlin  Triple kotlin  TypingIndicator kotlin  Unit kotlin  UserPreferences kotlin  	UserStats kotlin  
VirtualPet kotlin  VirtualPetWidget kotlin  Volatile kotlin  WelcomeStep kotlin  _uiState kotlin  all kotlin  android kotlin  androidx kotlin  animateFloatAsState kotlin  apply kotlin  arrayOf kotlin  asStateFlow kotlin  atTime kotlin  
background kotlin  bottomNavItems kotlin  budgetCategoryRepository kotlin  	clickable kotlin  
coerceAtLeast kotlin  coerceAtMost kotlin  coerceIn kotlin  com kotlin  
component1 kotlin  
component2 kotlin  contains kotlin  createNotificationChannel kotlin  creditCardRepository kotlin  currentBackStackEntryAsState kotlin  delay kotlin  	emptyList kotlin  expenseRepository kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  finish kotlin  firstOrNull kotlin  forEach kotlin  format kotlin  
formatDate kotlin  gamificationService kotlin  generateAIResponse kotlin  getCurrentPeriodDates kotlin  getCurrentPeriodValues kotlin  getValue kotlin  groupBy kotlin  height kotlin  infiniteRepeatable kotlin  invoke kotlin  isBlank kotlin  isDigit kotlin  isEmpty kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  java kotlin  joinToString kotlin  kotlinx kotlin  launch kotlin  let kotlin  listOf kotlin  loadCurrentPeriodExpenses kotlin  loadDebtData kotlin  	lowercase kotlin  	mapValues kotlin  minOf kotlin  minus kotlin  minusAssign kotlin  
mutableListOf kotlin  mutableMapOf kotlin  mutableStateOf kotlin  none kotlin  padding kotlin  plus kotlin  
plusAssign kotlin  provideDelegate kotlin  random kotlin  remember kotlin  repeat kotlin  replace kotlin  replaceFirstChar kotlin  set kotlin  setValue kotlin  size kotlin  sortedByDescending kotlin  split kotlin  sumOf kotlin  synchronized kotlin  take kotlin  takeIf kotlin  to kotlin  toDouble kotlin  toDoubleOrNull kotlin  toIntOrNull kotlin  toList kotlin  toLocalDateTime kotlin  toMap kotlin  toMutableMap kotlin  toRegex kotlin  trim kotlin  
trimIndent kotlin  tween kotlin  userPreferencesRepository kotlin  width kotlin  widthIn kotlin  
getISDigit kotlin.Char  
getIsDigit kotlin.Char  getLOWERCASE kotlin.Char  getLowercase kotlin.Char  isDigit kotlin.Char  getCOERCEAtLeast 
kotlin.Double  getCOERCEAtMost 
kotlin.Double  getCOERCEIn 
kotlin.Double  getCoerceAtLeast 
kotlin.Double  getCoerceAtMost 
kotlin.Double  getCoerceIn 
kotlin.Double  getLET 
kotlin.Double  getLet 
kotlin.Double  getMINUSAssign 
kotlin.Double  getMinusAssign 
kotlin.Double  
getPLUSAssign 
kotlin.Double  
getPlusAssign 
kotlin.Double  getSP 
kotlin.Double  getSp 
kotlin.Double  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
component1 kotlin.Pair  
component2 kotlin.Pair  second kotlin.Pair  getALL 
kotlin.String  getAll 
kotlin.String  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISBlank 
kotlin.String  
getISEmpty 
kotlin.String  
getISNotBlank 
kotlin.String  
getIsBlank 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotBlank 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  
getREPLACE 
kotlin.String  getREPLACEFirstChar 
kotlin.String  
getReplace 
kotlin.String  getReplaceFirstChar 
kotlin.String  getSPLIT 
kotlin.String  getSplit 
kotlin.String  getTAKE 
kotlin.String  	getTAKEIf 
kotlin.String  getTO 
kotlin.String  getTODouble 
kotlin.String  getTODoubleOrNull 
kotlin.String  getTOIntOrNull 
kotlin.String  
getTORegex 
kotlin.String  getTRIM 
kotlin.String  
getTRIMIndent 
kotlin.String  getTake 
kotlin.String  	getTakeIf 
kotlin.String  getTo 
kotlin.String  getToDouble 
kotlin.String  getToDoubleOrNull 
kotlin.String  getToIntOrNull 
kotlin.String  
getToRegex 
kotlin.String  getTrim 
kotlin.String  
getTrimIndent 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  ADHDFriendlyStep kotlin.annotation  
AICoachScreen kotlin.annotation  AICoachUiState kotlin.annotation  
AIInteraction kotlin.annotation  Achievement kotlin.annotation  AchievementBadge kotlin.annotation  	Alignment kotlin.annotation  Arrangement kotlin.annotation  Box kotlin.annotation  BudgetAmountItem kotlin.annotation  BudgetCategory kotlin.annotation  BudgetCategoryItem kotlin.annotation  BudgetOverviewCard kotlin.annotation  BudgetScreen kotlin.annotation  BudgetSetupStep kotlin.annotation  
BudgetUiState kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  
CHANNEL_ID kotlin.annotation  Card kotlin.annotation  ChatMessage kotlin.annotation  Checkbox kotlin.annotation  CircleShape kotlin.annotation  CircularProgressIndicator kotlin.annotation  Clock kotlin.annotation  Color kotlin.annotation  Column kotlin.annotation  CompleteStep kotlin.annotation  CompositionLocalProvider kotlin.annotation  Context kotlin.annotation  
Converters kotlin.annotation  
CreditCard kotlin.annotation  CreditCardItem kotlin.annotation  CreditCardSummaryCard kotlin.annotation  DashboardScreen kotlin.annotation  DashboardUiState kotlin.annotation  DateTimeUnit kotlin.annotation  DebtOverviewCard kotlin.annotation  
DebtScreen kotlin.annotation  
DebtSetupStep kotlin.annotation  DebtUiState kotlin.annotation  Double kotlin.annotation  EmptyBudgetState kotlin.annotation  EmptyStateCard kotlin.annotation  ErrorHandler kotlin.annotation  	Exception kotlin.annotation  Expense kotlin.annotation  ExpenseCategories kotlin.annotation  ExpenseItem kotlin.annotation  ExpenseUiState kotlin.annotation  ExpensesScreen kotlin.annotation  
FilterChip kotlin.annotation  FinancialGoalsStep kotlin.annotation  FloatingActionButton kotlin.annotation  FocusFlowApp kotlin.annotation  FocusFlowDatabase kotlin.annotation  FocusFlowTheme kotlin.annotation  
FontWeight kotlin.annotation  	GridCells kotlin.annotation  HabitLog kotlin.annotation  HabitStreakCard kotlin.annotation  HabitStreakItem kotlin.annotation  HabitsScreen kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  IllegalArgumentException kotlin.annotation  ImpulseControlQuestions kotlin.annotation  IncomeSetupStep kotlin.annotation  Intent kotlin.annotation  KeyboardOptions kotlin.annotation  KeyboardType kotlin.annotation  
LazyColumn kotlin.annotation  LazyRow kotlin.annotation  LazyVerticalGrid kotlin.annotation  LinearProgressIndicator kotlin.annotation  LocalContentColor kotlin.annotation  LocalContext kotlin.annotation  	LocalDate kotlin.annotation  
LocalDateTime kotlin.annotation  Log kotlin.annotation  MainActivity kotlin.annotation  MainUiState kotlin.annotation  
MaterialTheme kotlin.annotation  
MessageBubble kotlin.annotation  MessageInputField kotlin.annotation  Modifier kotlin.annotation  MotivationalQuoteCard kotlin.annotation  MutableStateFlow kotlin.annotation  NOTIFICATION_ID kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  NotificationService kotlin.annotation  NotificationSetupStep kotlin.annotation  NumberFormatException kotlin.annotation  OnConflictStrategy kotlin.annotation  OnboardingProgressIndicator kotlin.annotation  OnboardingStep kotlin.annotation  OnboardingUiState kotlin.annotation  OutlinedButton kotlin.annotation  OutlinedTextField kotlin.annotation  Pair kotlin.annotation  PayoffCalculation kotlin.annotation  
PayoffStep kotlin.annotation  PayoffStrategy kotlin.annotation  
PendingIntent kotlin.annotation  PersonalGoalsStep kotlin.annotation  ProgressAchievementsCard kotlin.annotation  QuickCategoryOverview kotlin.annotation  RadioButton kotlin.annotation  Regex kotlin.annotation  
RepeatMode kotlin.annotation  Room kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  START_NOT_STICKY kotlin.annotation  SafeToSpendWidget kotlin.annotation  Screen kotlin.annotation  SecurityException kotlin.annotation  SingletonComponent kotlin.annotation  SnackbarDuration kotlin.annotation  Spacer kotlin.annotation  SpendingSummaryCard kotlin.annotation  String kotlin.annotation  StringListConverter kotlin.annotation  SuggestedPromptCard kotlin.annotation  SuggestedPromptsSection kotlin.annotation  Surface kotlin.annotation  Switch kotlin.annotation  System kotlin.annotation  Task kotlin.annotation  TaskItem kotlin.annotation  TasksScreen kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  
TextButton kotlin.annotation  TimeZone kotlin.annotation  TodaysTasksCard kotlin.annotation  Triple kotlin.annotation  TypingIndicator kotlin.annotation  UserPreferences kotlin.annotation  	UserStats kotlin.annotation  
VirtualPet kotlin.annotation  VirtualPetWidget kotlin.annotation  Volatile kotlin.annotation  WelcomeStep kotlin.annotation  _uiState kotlin.annotation  all kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  animateFloatAsState kotlin.annotation  apply kotlin.annotation  asStateFlow kotlin.annotation  atTime kotlin.annotation  
background kotlin.annotation  bottomNavItems kotlin.annotation  budgetCategoryRepository kotlin.annotation  	clickable kotlin.annotation  
coerceAtLeast kotlin.annotation  coerceAtMost kotlin.annotation  coerceIn kotlin.annotation  com kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  contains kotlin.annotation  createNotificationChannel kotlin.annotation  creditCardRepository kotlin.annotation  currentBackStackEntryAsState kotlin.annotation  delay kotlin.annotation  	emptyList kotlin.annotation  expenseRepository kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  finish kotlin.annotation  firstOrNull kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  
formatDate kotlin.annotation  gamificationService kotlin.annotation  generateAIResponse kotlin.annotation  getCurrentPeriodDates kotlin.annotation  getCurrentPeriodValues kotlin.annotation  getValue kotlin.annotation  groupBy kotlin.annotation  height kotlin.annotation  infiniteRepeatable kotlin.annotation  invoke kotlin.annotation  isBlank kotlin.annotation  isDigit kotlin.annotation  isEmpty kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  kotlinx kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  loadCurrentPeriodExpenses kotlin.annotation  loadDebtData kotlin.annotation  	lowercase kotlin.annotation  	mapValues kotlin.annotation  minOf kotlin.annotation  minus kotlin.annotation  minusAssign kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  mutableStateOf kotlin.annotation  none kotlin.annotation  padding kotlin.annotation  plus kotlin.annotation  
plusAssign kotlin.annotation  provideDelegate kotlin.annotation  random kotlin.annotation  remember kotlin.annotation  repeat kotlin.annotation  replace kotlin.annotation  replaceFirstChar kotlin.annotation  set kotlin.annotation  setValue kotlin.annotation  size kotlin.annotation  sortedByDescending kotlin.annotation  split kotlin.annotation  sumOf kotlin.annotation  synchronized kotlin.annotation  take kotlin.annotation  takeIf kotlin.annotation  to kotlin.annotation  toDouble kotlin.annotation  toDoubleOrNull kotlin.annotation  toIntOrNull kotlin.annotation  toList kotlin.annotation  toLocalDateTime kotlin.annotation  toMap kotlin.annotation  toMutableMap kotlin.annotation  toRegex kotlin.annotation  trim kotlin.annotation  
trimIndent kotlin.annotation  tween kotlin.annotation  userPreferencesRepository kotlin.annotation  width kotlin.annotation  widthIn kotlin.annotation  ADHDFriendlyStep kotlin.collections  
AICoachScreen kotlin.collections  AICoachUiState kotlin.collections  
AIInteraction kotlin.collections  Achievement kotlin.collections  AchievementBadge kotlin.collections  	Alignment kotlin.collections  Arrangement kotlin.collections  Box kotlin.collections  BudgetAmountItem kotlin.collections  BudgetCategory kotlin.collections  BudgetCategoryItem kotlin.collections  BudgetOverviewCard kotlin.collections  BudgetScreen kotlin.collections  BudgetSetupStep kotlin.collections  
BudgetUiState kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  
CHANNEL_ID kotlin.collections  Card kotlin.collections  ChatMessage kotlin.collections  Checkbox kotlin.collections  CircleShape kotlin.collections  CircularProgressIndicator kotlin.collections  Clock kotlin.collections  Color kotlin.collections  Column kotlin.collections  CompleteStep kotlin.collections  CompositionLocalProvider kotlin.collections  Context kotlin.collections  
Converters kotlin.collections  
CreditCard kotlin.collections  CreditCardItem kotlin.collections  CreditCardSummaryCard kotlin.collections  DashboardScreen kotlin.collections  DashboardUiState kotlin.collections  DateTimeUnit kotlin.collections  DebtOverviewCard kotlin.collections  
DebtScreen kotlin.collections  
DebtSetupStep kotlin.collections  DebtUiState kotlin.collections  Double kotlin.collections  EmptyBudgetState kotlin.collections  EmptyStateCard kotlin.collections  ErrorHandler kotlin.collections  	Exception kotlin.collections  Expense kotlin.collections  ExpenseCategories kotlin.collections  ExpenseItem kotlin.collections  ExpenseUiState kotlin.collections  ExpensesScreen kotlin.collections  
FilterChip kotlin.collections  FinancialGoalsStep kotlin.collections  FloatingActionButton kotlin.collections  FocusFlowApp kotlin.collections  FocusFlowDatabase kotlin.collections  FocusFlowTheme kotlin.collections  
FontWeight kotlin.collections  	GridCells kotlin.collections  HabitLog kotlin.collections  HabitStreakCard kotlin.collections  HabitStreakItem kotlin.collections  HabitsScreen kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  IllegalArgumentException kotlin.collections  ImpulseControlQuestions kotlin.collections  IncomeSetupStep kotlin.collections  Intent kotlin.collections  KeyboardOptions kotlin.collections  KeyboardType kotlin.collections  
LazyColumn kotlin.collections  LazyRow kotlin.collections  LazyVerticalGrid kotlin.collections  LinearProgressIndicator kotlin.collections  List kotlin.collections  LocalContentColor kotlin.collections  LocalContext kotlin.collections  	LocalDate kotlin.collections  
LocalDateTime kotlin.collections  Log kotlin.collections  MainActivity kotlin.collections  MainUiState kotlin.collections  
MaterialTheme kotlin.collections  
MessageBubble kotlin.collections  MessageInputField kotlin.collections  Modifier kotlin.collections  MotivationalQuoteCard kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  NOTIFICATION_ID kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  NotificationService kotlin.collections  NotificationSetupStep kotlin.collections  NumberFormatException kotlin.collections  OnConflictStrategy kotlin.collections  OnboardingProgressIndicator kotlin.collections  OnboardingStep kotlin.collections  OnboardingUiState kotlin.collections  OutlinedButton kotlin.collections  OutlinedTextField kotlin.collections  Pair kotlin.collections  PayoffCalculation kotlin.collections  
PayoffStep kotlin.collections  PayoffStrategy kotlin.collections  
PendingIntent kotlin.collections  PersonalGoalsStep kotlin.collections  ProgressAchievementsCard kotlin.collections  QuickCategoryOverview kotlin.collections  RadioButton kotlin.collections  Regex kotlin.collections  
RepeatMode kotlin.collections  Room kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  START_NOT_STICKY kotlin.collections  SafeToSpendWidget kotlin.collections  Screen kotlin.collections  SecurityException kotlin.collections  SingletonComponent kotlin.collections  SnackbarDuration kotlin.collections  Spacer kotlin.collections  SpendingSummaryCard kotlin.collections  String kotlin.collections  StringListConverter kotlin.collections  SuggestedPromptCard kotlin.collections  SuggestedPromptsSection kotlin.collections  Surface kotlin.collections  Switch kotlin.collections  System kotlin.collections  Task kotlin.collections  TaskItem kotlin.collections  TasksScreen kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  
TextButton kotlin.collections  TimeZone kotlin.collections  TodaysTasksCard kotlin.collections  Triple kotlin.collections  TypingIndicator kotlin.collections  UserPreferences kotlin.collections  	UserStats kotlin.collections  
VirtualPet kotlin.collections  VirtualPetWidget kotlin.collections  Volatile kotlin.collections  WelcomeStep kotlin.collections  _uiState kotlin.collections  all kotlin.collections  android kotlin.collections  androidx kotlin.collections  animateFloatAsState kotlin.collections  apply kotlin.collections  asStateFlow kotlin.collections  atTime kotlin.collections  
background kotlin.collections  bottomNavItems kotlin.collections  budgetCategoryRepository kotlin.collections  	clickable kotlin.collections  
coerceAtLeast kotlin.collections  coerceAtMost kotlin.collections  coerceIn kotlin.collections  com kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  createNotificationChannel kotlin.collections  creditCardRepository kotlin.collections  currentBackStackEntryAsState kotlin.collections  delay kotlin.collections  	emptyList kotlin.collections  expenseRepository kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  finish kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  format kotlin.collections  
formatDate kotlin.collections  gamificationService kotlin.collections  generateAIResponse kotlin.collections  getCurrentPeriodDates kotlin.collections  getCurrentPeriodValues kotlin.collections  getValue kotlin.collections  groupBy kotlin.collections  height kotlin.collections  infiniteRepeatable kotlin.collections  invoke kotlin.collections  isBlank kotlin.collections  isDigit kotlin.collections  isEmpty kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  joinToString kotlin.collections  kotlinx kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  loadCurrentPeriodExpenses kotlin.collections  loadDebtData kotlin.collections  	lowercase kotlin.collections  	mapValues kotlin.collections  minOf kotlin.collections  minus kotlin.collections  minusAssign kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableStateOf kotlin.collections  none kotlin.collections  padding kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  provideDelegate kotlin.collections  random kotlin.collections  remember kotlin.collections  repeat kotlin.collections  replace kotlin.collections  replaceFirstChar kotlin.collections  set kotlin.collections  setValue kotlin.collections  size kotlin.collections  sortedByDescending kotlin.collections  split kotlin.collections  sumOf kotlin.collections  sumOfDouble kotlin.collections  synchronized kotlin.collections  take kotlin.collections  takeIf kotlin.collections  to kotlin.collections  toDouble kotlin.collections  toDoubleOrNull kotlin.collections  toIntOrNull kotlin.collections  toList kotlin.collections  toLocalDateTime kotlin.collections  toMap kotlin.collections  toMutableMap kotlin.collections  toRegex kotlin.collections  trim kotlin.collections  
trimIndent kotlin.collections  tween kotlin.collections  userPreferencesRepository kotlin.collections  width kotlin.collections  widthIn kotlin.collections  getFIRSTOrNull kotlin.collections.List  getFirstOrNull kotlin.collections.List  
getGROUPBy kotlin.collections.List  
getGroupBy kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  getMINUS kotlin.collections.List  getMinus kotlin.collections.List  getNONE kotlin.collections.List  getNone kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  	getRANDOM kotlin.collections.List  	getRandom kotlin.collections.List  getSORTEDByDescending kotlin.collections.List  getSUMOf kotlin.collections.List  getSortedByDescending kotlin.collections.List  getSumOf kotlin.collections.List  getTAKE kotlin.collections.List  getTOMap kotlin.collections.List  getTake kotlin.collections.List  getToMap kotlin.collections.List  
isNotEmpty kotlin.collections.List  Entry kotlin.collections.Map  getMAPValues kotlin.collections.Map  getMapValues kotlin.collections.Map  	getTOList kotlin.collections.Map  getTOMutableMap kotlin.collections.Map  	getToList kotlin.collections.Map  getToMutableMap kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  	getTOList kotlin.collections.MutableMap  	getToList kotlin.collections.MutableMap  ADHDFriendlyStep kotlin.comparisons  
AICoachScreen kotlin.comparisons  AICoachUiState kotlin.comparisons  
AIInteraction kotlin.comparisons  Achievement kotlin.comparisons  AchievementBadge kotlin.comparisons  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  Box kotlin.comparisons  BudgetAmountItem kotlin.comparisons  BudgetCategory kotlin.comparisons  BudgetCategoryItem kotlin.comparisons  BudgetOverviewCard kotlin.comparisons  BudgetScreen kotlin.comparisons  BudgetSetupStep kotlin.comparisons  
BudgetUiState kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  Card kotlin.comparisons  ChatMessage kotlin.comparisons  Checkbox kotlin.comparisons  CircleShape kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Clock kotlin.comparisons  Color kotlin.comparisons  Column kotlin.comparisons  CompleteStep kotlin.comparisons  CompositionLocalProvider kotlin.comparisons  Context kotlin.comparisons  
Converters kotlin.comparisons  
CreditCard kotlin.comparisons  CreditCardItem kotlin.comparisons  CreditCardSummaryCard kotlin.comparisons  DashboardScreen kotlin.comparisons  DashboardUiState kotlin.comparisons  DateTimeUnit kotlin.comparisons  DebtOverviewCard kotlin.comparisons  
DebtScreen kotlin.comparisons  
DebtSetupStep kotlin.comparisons  DebtUiState kotlin.comparisons  Double kotlin.comparisons  EmptyBudgetState kotlin.comparisons  EmptyStateCard kotlin.comparisons  ErrorHandler kotlin.comparisons  	Exception kotlin.comparisons  Expense kotlin.comparisons  ExpenseCategories kotlin.comparisons  ExpenseItem kotlin.comparisons  ExpenseUiState kotlin.comparisons  ExpensesScreen kotlin.comparisons  
FilterChip kotlin.comparisons  FinancialGoalsStep kotlin.comparisons  FloatingActionButton kotlin.comparisons  FocusFlowApp kotlin.comparisons  FocusFlowDatabase kotlin.comparisons  FocusFlowTheme kotlin.comparisons  
FontWeight kotlin.comparisons  	GridCells kotlin.comparisons  HabitLog kotlin.comparisons  HabitStreakCard kotlin.comparisons  HabitStreakItem kotlin.comparisons  HabitsScreen kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  IllegalArgumentException kotlin.comparisons  ImpulseControlQuestions kotlin.comparisons  IncomeSetupStep kotlin.comparisons  Intent kotlin.comparisons  KeyboardOptions kotlin.comparisons  KeyboardType kotlin.comparisons  
LazyColumn kotlin.comparisons  LazyRow kotlin.comparisons  LazyVerticalGrid kotlin.comparisons  LinearProgressIndicator kotlin.comparisons  LocalContentColor kotlin.comparisons  LocalContext kotlin.comparisons  	LocalDate kotlin.comparisons  
LocalDateTime kotlin.comparisons  Log kotlin.comparisons  MainActivity kotlin.comparisons  MainUiState kotlin.comparisons  
MaterialTheme kotlin.comparisons  
MessageBubble kotlin.comparisons  MessageInputField kotlin.comparisons  Modifier kotlin.comparisons  MotivationalQuoteCard kotlin.comparisons  MutableStateFlow kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  NotificationService kotlin.comparisons  NotificationSetupStep kotlin.comparisons  NumberFormatException kotlin.comparisons  OnConflictStrategy kotlin.comparisons  OnboardingProgressIndicator kotlin.comparisons  OnboardingStep kotlin.comparisons  OnboardingUiState kotlin.comparisons  OutlinedButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  Pair kotlin.comparisons  PayoffCalculation kotlin.comparisons  
PayoffStep kotlin.comparisons  PayoffStrategy kotlin.comparisons  
PendingIntent kotlin.comparisons  PersonalGoalsStep kotlin.comparisons  ProgressAchievementsCard kotlin.comparisons  QuickCategoryOverview kotlin.comparisons  RadioButton kotlin.comparisons  Regex kotlin.comparisons  
RepeatMode kotlin.comparisons  Room kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  START_NOT_STICKY kotlin.comparisons  SafeToSpendWidget kotlin.comparisons  Screen kotlin.comparisons  SecurityException kotlin.comparisons  SingletonComponent kotlin.comparisons  SnackbarDuration kotlin.comparisons  Spacer kotlin.comparisons  SpendingSummaryCard kotlin.comparisons  String kotlin.comparisons  StringListConverter kotlin.comparisons  SuggestedPromptCard kotlin.comparisons  SuggestedPromptsSection kotlin.comparisons  Surface kotlin.comparisons  Switch kotlin.comparisons  System kotlin.comparisons  Task kotlin.comparisons  TaskItem kotlin.comparisons  TasksScreen kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  
TextButton kotlin.comparisons  TimeZone kotlin.comparisons  TodaysTasksCard kotlin.comparisons  Triple kotlin.comparisons  TypingIndicator kotlin.comparisons  UserPreferences kotlin.comparisons  	UserStats kotlin.comparisons  
VirtualPet kotlin.comparisons  VirtualPetWidget kotlin.comparisons  Volatile kotlin.comparisons  WelcomeStep kotlin.comparisons  _uiState kotlin.comparisons  all kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  animateFloatAsState kotlin.comparisons  apply kotlin.comparisons  asStateFlow kotlin.comparisons  atTime kotlin.comparisons  
background kotlin.comparisons  bottomNavItems kotlin.comparisons  budgetCategoryRepository kotlin.comparisons  	clickable kotlin.comparisons  
coerceAtLeast kotlin.comparisons  coerceAtMost kotlin.comparisons  coerceIn kotlin.comparisons  com kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  contains kotlin.comparisons  createNotificationChannel kotlin.comparisons  creditCardRepository kotlin.comparisons  currentBackStackEntryAsState kotlin.comparisons  delay kotlin.comparisons  	emptyList kotlin.comparisons  expenseRepository kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  finish kotlin.comparisons  firstOrNull kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  
formatDate kotlin.comparisons  gamificationService kotlin.comparisons  generateAIResponse kotlin.comparisons  getCurrentPeriodDates kotlin.comparisons  getCurrentPeriodValues kotlin.comparisons  getValue kotlin.comparisons  groupBy kotlin.comparisons  height kotlin.comparisons  infiniteRepeatable kotlin.comparisons  invoke kotlin.comparisons  isBlank kotlin.comparisons  isDigit kotlin.comparisons  isEmpty kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  kotlinx kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  loadCurrentPeriodExpenses kotlin.comparisons  loadDebtData kotlin.comparisons  	lowercase kotlin.comparisons  	mapValues kotlin.comparisons  minOf kotlin.comparisons  minus kotlin.comparisons  minusAssign kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  mutableStateOf kotlin.comparisons  none kotlin.comparisons  padding kotlin.comparisons  plus kotlin.comparisons  
plusAssign kotlin.comparisons  provideDelegate kotlin.comparisons  random kotlin.comparisons  remember kotlin.comparisons  repeat kotlin.comparisons  replace kotlin.comparisons  replaceFirstChar kotlin.comparisons  set kotlin.comparisons  setValue kotlin.comparisons  size kotlin.comparisons  sortedByDescending kotlin.comparisons  split kotlin.comparisons  sumOf kotlin.comparisons  synchronized kotlin.comparisons  take kotlin.comparisons  takeIf kotlin.comparisons  to kotlin.comparisons  toDouble kotlin.comparisons  toDoubleOrNull kotlin.comparisons  toIntOrNull kotlin.comparisons  toList kotlin.comparisons  toLocalDateTime kotlin.comparisons  toMap kotlin.comparisons  toMutableMap kotlin.comparisons  toRegex kotlin.comparisons  trim kotlin.comparisons  
trimIndent kotlin.comparisons  tween kotlin.comparisons  userPreferencesRepository kotlin.comparisons  width kotlin.comparisons  widthIn kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ADHDFriendlyStep 	kotlin.io  
AICoachScreen 	kotlin.io  AICoachUiState 	kotlin.io  
AIInteraction 	kotlin.io  Achievement 	kotlin.io  AchievementBadge 	kotlin.io  	Alignment 	kotlin.io  Arrangement 	kotlin.io  Box 	kotlin.io  BudgetAmountItem 	kotlin.io  BudgetCategory 	kotlin.io  BudgetCategoryItem 	kotlin.io  BudgetOverviewCard 	kotlin.io  BudgetScreen 	kotlin.io  BudgetSetupStep 	kotlin.io  
BudgetUiState 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  
CHANNEL_ID 	kotlin.io  Card 	kotlin.io  ChatMessage 	kotlin.io  Checkbox 	kotlin.io  CircleShape 	kotlin.io  CircularProgressIndicator 	kotlin.io  Clock 	kotlin.io  Color 	kotlin.io  Column 	kotlin.io  CompleteStep 	kotlin.io  CompositionLocalProvider 	kotlin.io  Context 	kotlin.io  
Converters 	kotlin.io  
CreditCard 	kotlin.io  CreditCardItem 	kotlin.io  CreditCardSummaryCard 	kotlin.io  DashboardScreen 	kotlin.io  DashboardUiState 	kotlin.io  DateTimeUnit 	kotlin.io  DebtOverviewCard 	kotlin.io  
DebtScreen 	kotlin.io  
DebtSetupStep 	kotlin.io  DebtUiState 	kotlin.io  Double 	kotlin.io  EmptyBudgetState 	kotlin.io  EmptyStateCard 	kotlin.io  ErrorHandler 	kotlin.io  	Exception 	kotlin.io  Expense 	kotlin.io  ExpenseCategories 	kotlin.io  ExpenseItem 	kotlin.io  ExpenseUiState 	kotlin.io  ExpensesScreen 	kotlin.io  
FilterChip 	kotlin.io  FinancialGoalsStep 	kotlin.io  FloatingActionButton 	kotlin.io  FocusFlowApp 	kotlin.io  FocusFlowDatabase 	kotlin.io  FocusFlowTheme 	kotlin.io  
FontWeight 	kotlin.io  	GridCells 	kotlin.io  HabitLog 	kotlin.io  HabitStreakCard 	kotlin.io  HabitStreakItem 	kotlin.io  HabitsScreen 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  IllegalArgumentException 	kotlin.io  ImpulseControlQuestions 	kotlin.io  IncomeSetupStep 	kotlin.io  Intent 	kotlin.io  KeyboardOptions 	kotlin.io  KeyboardType 	kotlin.io  
LazyColumn 	kotlin.io  LazyRow 	kotlin.io  LazyVerticalGrid 	kotlin.io  LinearProgressIndicator 	kotlin.io  LocalContentColor 	kotlin.io  LocalContext 	kotlin.io  	LocalDate 	kotlin.io  
LocalDateTime 	kotlin.io  Log 	kotlin.io  MainActivity 	kotlin.io  MainUiState 	kotlin.io  
MaterialTheme 	kotlin.io  
MessageBubble 	kotlin.io  MessageInputField 	kotlin.io  Modifier 	kotlin.io  MotivationalQuoteCard 	kotlin.io  MutableStateFlow 	kotlin.io  NOTIFICATION_ID 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  NotificationService 	kotlin.io  NotificationSetupStep 	kotlin.io  NumberFormatException 	kotlin.io  OnConflictStrategy 	kotlin.io  OnboardingProgressIndicator 	kotlin.io  OnboardingStep 	kotlin.io  OnboardingUiState 	kotlin.io  OutlinedButton 	kotlin.io  OutlinedTextField 	kotlin.io  Pair 	kotlin.io  PayoffCalculation 	kotlin.io  
PayoffStep 	kotlin.io  PayoffStrategy 	kotlin.io  
PendingIntent 	kotlin.io  PersonalGoalsStep 	kotlin.io  ProgressAchievementsCard 	kotlin.io  QuickCategoryOverview 	kotlin.io  RadioButton 	kotlin.io  Regex 	kotlin.io  
RepeatMode 	kotlin.io  Room 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  START_NOT_STICKY 	kotlin.io  SafeToSpendWidget 	kotlin.io  Screen 	kotlin.io  SecurityException 	kotlin.io  SingletonComponent 	kotlin.io  SnackbarDuration 	kotlin.io  Spacer 	kotlin.io  SpendingSummaryCard 	kotlin.io  String 	kotlin.io  StringListConverter 	kotlin.io  SuggestedPromptCard 	kotlin.io  SuggestedPromptsSection 	kotlin.io  Surface 	kotlin.io  Switch 	kotlin.io  System 	kotlin.io  Task 	kotlin.io  TaskItem 	kotlin.io  TasksScreen 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  
TextButton 	kotlin.io  TimeZone 	kotlin.io  TodaysTasksCard 	kotlin.io  Triple 	kotlin.io  TypingIndicator 	kotlin.io  UserPreferences 	kotlin.io  	UserStats 	kotlin.io  
VirtualPet 	kotlin.io  VirtualPetWidget 	kotlin.io  Volatile 	kotlin.io  WelcomeStep 	kotlin.io  _uiState 	kotlin.io  all 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  animateFloatAsState 	kotlin.io  apply 	kotlin.io  asStateFlow 	kotlin.io  atTime 	kotlin.io  
background 	kotlin.io  bottomNavItems 	kotlin.io  budgetCategoryRepository 	kotlin.io  	clickable 	kotlin.io  
coerceAtLeast 	kotlin.io  coerceAtMost 	kotlin.io  coerceIn 	kotlin.io  com 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  contains 	kotlin.io  createNotificationChannel 	kotlin.io  creditCardRepository 	kotlin.io  currentBackStackEntryAsState 	kotlin.io  delay 	kotlin.io  	emptyList 	kotlin.io  expenseRepository 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  finish 	kotlin.io  firstOrNull 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  
formatDate 	kotlin.io  gamificationService 	kotlin.io  generateAIResponse 	kotlin.io  getCurrentPeriodDates 	kotlin.io  getCurrentPeriodValues 	kotlin.io  getValue 	kotlin.io  groupBy 	kotlin.io  height 	kotlin.io  infiniteRepeatable 	kotlin.io  invoke 	kotlin.io  isBlank 	kotlin.io  isDigit 	kotlin.io  isEmpty 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  kotlinx 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  loadCurrentPeriodExpenses 	kotlin.io  loadDebtData 	kotlin.io  	lowercase 	kotlin.io  	mapValues 	kotlin.io  minOf 	kotlin.io  minus 	kotlin.io  minusAssign 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  mutableStateOf 	kotlin.io  none 	kotlin.io  padding 	kotlin.io  plus 	kotlin.io  
plusAssign 	kotlin.io  provideDelegate 	kotlin.io  random 	kotlin.io  remember 	kotlin.io  repeat 	kotlin.io  replace 	kotlin.io  replaceFirstChar 	kotlin.io  set 	kotlin.io  setValue 	kotlin.io  size 	kotlin.io  sortedByDescending 	kotlin.io  split 	kotlin.io  sumOf 	kotlin.io  synchronized 	kotlin.io  take 	kotlin.io  takeIf 	kotlin.io  to 	kotlin.io  toDouble 	kotlin.io  toDoubleOrNull 	kotlin.io  toIntOrNull 	kotlin.io  toList 	kotlin.io  toLocalDateTime 	kotlin.io  toMap 	kotlin.io  toMutableMap 	kotlin.io  toRegex 	kotlin.io  trim 	kotlin.io  
trimIndent 	kotlin.io  tween 	kotlin.io  userPreferencesRepository 	kotlin.io  width 	kotlin.io  widthIn 	kotlin.io  ADHDFriendlyStep 
kotlin.jvm  
AICoachScreen 
kotlin.jvm  AICoachUiState 
kotlin.jvm  
AIInteraction 
kotlin.jvm  Achievement 
kotlin.jvm  AchievementBadge 
kotlin.jvm  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  Box 
kotlin.jvm  BudgetAmountItem 
kotlin.jvm  BudgetCategory 
kotlin.jvm  BudgetCategoryItem 
kotlin.jvm  BudgetOverviewCard 
kotlin.jvm  BudgetScreen 
kotlin.jvm  BudgetSetupStep 
kotlin.jvm  
BudgetUiState 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  Card 
kotlin.jvm  ChatMessage 
kotlin.jvm  Checkbox 
kotlin.jvm  CircleShape 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Clock 
kotlin.jvm  Color 
kotlin.jvm  Column 
kotlin.jvm  CompleteStep 
kotlin.jvm  CompositionLocalProvider 
kotlin.jvm  Context 
kotlin.jvm  
Converters 
kotlin.jvm  
CreditCard 
kotlin.jvm  CreditCardItem 
kotlin.jvm  CreditCardSummaryCard 
kotlin.jvm  DashboardScreen 
kotlin.jvm  DashboardUiState 
kotlin.jvm  DateTimeUnit 
kotlin.jvm  DebtOverviewCard 
kotlin.jvm  
DebtScreen 
kotlin.jvm  
DebtSetupStep 
kotlin.jvm  DebtUiState 
kotlin.jvm  Double 
kotlin.jvm  EmptyBudgetState 
kotlin.jvm  EmptyStateCard 
kotlin.jvm  ErrorHandler 
kotlin.jvm  	Exception 
kotlin.jvm  Expense 
kotlin.jvm  ExpenseCategories 
kotlin.jvm  ExpenseItem 
kotlin.jvm  ExpenseUiState 
kotlin.jvm  ExpensesScreen 
kotlin.jvm  
FilterChip 
kotlin.jvm  FinancialGoalsStep 
kotlin.jvm  FloatingActionButton 
kotlin.jvm  FocusFlowApp 
kotlin.jvm  FocusFlowDatabase 
kotlin.jvm  FocusFlowTheme 
kotlin.jvm  
FontWeight 
kotlin.jvm  	GridCells 
kotlin.jvm  HabitLog 
kotlin.jvm  HabitStreakCard 
kotlin.jvm  HabitStreakItem 
kotlin.jvm  HabitsScreen 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  IllegalArgumentException 
kotlin.jvm  ImpulseControlQuestions 
kotlin.jvm  IncomeSetupStep 
kotlin.jvm  Intent 
kotlin.jvm  KeyboardOptions 
kotlin.jvm  KeyboardType 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LazyRow 
kotlin.jvm  LazyVerticalGrid 
kotlin.jvm  LinearProgressIndicator 
kotlin.jvm  LocalContentColor 
kotlin.jvm  LocalContext 
kotlin.jvm  	LocalDate 
kotlin.jvm  
LocalDateTime 
kotlin.jvm  Log 
kotlin.jvm  MainActivity 
kotlin.jvm  MainUiState 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  
MessageBubble 
kotlin.jvm  MessageInputField 
kotlin.jvm  Modifier 
kotlin.jvm  MotivationalQuoteCard 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  NotificationService 
kotlin.jvm  NotificationSetupStep 
kotlin.jvm  NumberFormatException 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  OnboardingProgressIndicator 
kotlin.jvm  OnboardingStep 
kotlin.jvm  OnboardingUiState 
kotlin.jvm  OutlinedButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  Pair 
kotlin.jvm  PayoffCalculation 
kotlin.jvm  
PayoffStep 
kotlin.jvm  PayoffStrategy 
kotlin.jvm  
PendingIntent 
kotlin.jvm  PersonalGoalsStep 
kotlin.jvm  ProgressAchievementsCard 
kotlin.jvm  QuickCategoryOverview 
kotlin.jvm  RadioButton 
kotlin.jvm  Regex 
kotlin.jvm  
RepeatMode 
kotlin.jvm  Room 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  START_NOT_STICKY 
kotlin.jvm  SafeToSpendWidget 
kotlin.jvm  Screen 
kotlin.jvm  SecurityException 
kotlin.jvm  SingletonComponent 
kotlin.jvm  SnackbarDuration 
kotlin.jvm  Spacer 
kotlin.jvm  SpendingSummaryCard 
kotlin.jvm  String 
kotlin.jvm  StringListConverter 
kotlin.jvm  SuggestedPromptCard 
kotlin.jvm  SuggestedPromptsSection 
kotlin.jvm  Surface 
kotlin.jvm  Switch 
kotlin.jvm  System 
kotlin.jvm  Task 
kotlin.jvm  TaskItem 
kotlin.jvm  TasksScreen 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  
TextButton 
kotlin.jvm  TimeZone 
kotlin.jvm  TodaysTasksCard 
kotlin.jvm  Triple 
kotlin.jvm  TypingIndicator 
kotlin.jvm  UserPreferences 
kotlin.jvm  	UserStats 
kotlin.jvm  
VirtualPet 
kotlin.jvm  VirtualPetWidget 
kotlin.jvm  Volatile 
kotlin.jvm  WelcomeStep 
kotlin.jvm  _uiState 
kotlin.jvm  all 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  animateFloatAsState 
kotlin.jvm  apply 
kotlin.jvm  asStateFlow 
kotlin.jvm  atTime 
kotlin.jvm  
background 
kotlin.jvm  bottomNavItems 
kotlin.jvm  budgetCategoryRepository 
kotlin.jvm  	clickable 
kotlin.jvm  
coerceAtLeast 
kotlin.jvm  coerceAtMost 
kotlin.jvm  coerceIn 
kotlin.jvm  com 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  contains 
kotlin.jvm  createNotificationChannel 
kotlin.jvm  creditCardRepository 
kotlin.jvm  currentBackStackEntryAsState 
kotlin.jvm  delay 
kotlin.jvm  	emptyList 
kotlin.jvm  expenseRepository 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  finish 
kotlin.jvm  firstOrNull 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  
formatDate 
kotlin.jvm  gamificationService 
kotlin.jvm  generateAIResponse 
kotlin.jvm  getCurrentPeriodDates 
kotlin.jvm  getCurrentPeriodValues 
kotlin.jvm  getValue 
kotlin.jvm  groupBy 
kotlin.jvm  height 
kotlin.jvm  infiniteRepeatable 
kotlin.jvm  invoke 
kotlin.jvm  isBlank 
kotlin.jvm  isDigit 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  kotlinx 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  loadCurrentPeriodExpenses 
kotlin.jvm  loadDebtData 
kotlin.jvm  	lowercase 
kotlin.jvm  	mapValues 
kotlin.jvm  minOf 
kotlin.jvm  minus 
kotlin.jvm  minusAssign 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  mutableStateOf 
kotlin.jvm  none 
kotlin.jvm  padding 
kotlin.jvm  plus 
kotlin.jvm  
plusAssign 
kotlin.jvm  provideDelegate 
kotlin.jvm  random 
kotlin.jvm  remember 
kotlin.jvm  repeat 
kotlin.jvm  replace 
kotlin.jvm  replaceFirstChar 
kotlin.jvm  set 
kotlin.jvm  setValue 
kotlin.jvm  size 
kotlin.jvm  sortedByDescending 
kotlin.jvm  split 
kotlin.jvm  sumOf 
kotlin.jvm  synchronized 
kotlin.jvm  take 
kotlin.jvm  takeIf 
kotlin.jvm  to 
kotlin.jvm  toDouble 
kotlin.jvm  toDoubleOrNull 
kotlin.jvm  toIntOrNull 
kotlin.jvm  toList 
kotlin.jvm  toLocalDateTime 
kotlin.jvm  toMap 
kotlin.jvm  toMutableMap 
kotlin.jvm  toRegex 
kotlin.jvm  trim 
kotlin.jvm  
trimIndent 
kotlin.jvm  tween 
kotlin.jvm  userPreferencesRepository 
kotlin.jvm  width 
kotlin.jvm  widthIn 
kotlin.jvm  ADHDFriendlyStep 
kotlin.ranges  
AICoachScreen 
kotlin.ranges  AICoachUiState 
kotlin.ranges  
AIInteraction 
kotlin.ranges  Achievement 
kotlin.ranges  AchievementBadge 
kotlin.ranges  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  Box 
kotlin.ranges  BudgetAmountItem 
kotlin.ranges  BudgetCategory 
kotlin.ranges  BudgetCategoryItem 
kotlin.ranges  BudgetOverviewCard 
kotlin.ranges  BudgetScreen 
kotlin.ranges  BudgetSetupStep 
kotlin.ranges  
BudgetUiState 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  Card 
kotlin.ranges  ChatMessage 
kotlin.ranges  Checkbox 
kotlin.ranges  CircleShape 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Clock 
kotlin.ranges  Color 
kotlin.ranges  Column 
kotlin.ranges  CompleteStep 
kotlin.ranges  CompositionLocalProvider 
kotlin.ranges  Context 
kotlin.ranges  
Converters 
kotlin.ranges  
CreditCard 
kotlin.ranges  CreditCardItem 
kotlin.ranges  CreditCardSummaryCard 
kotlin.ranges  DashboardScreen 
kotlin.ranges  DashboardUiState 
kotlin.ranges  DateTimeUnit 
kotlin.ranges  DebtOverviewCard 
kotlin.ranges  
DebtScreen 
kotlin.ranges  
DebtSetupStep 
kotlin.ranges  DebtUiState 
kotlin.ranges  Double 
kotlin.ranges  EmptyBudgetState 
kotlin.ranges  EmptyStateCard 
kotlin.ranges  ErrorHandler 
kotlin.ranges  	Exception 
kotlin.ranges  Expense 
kotlin.ranges  ExpenseCategories 
kotlin.ranges  ExpenseItem 
kotlin.ranges  ExpenseUiState 
kotlin.ranges  ExpensesScreen 
kotlin.ranges  
FilterChip 
kotlin.ranges  FinancialGoalsStep 
kotlin.ranges  FloatingActionButton 
kotlin.ranges  FocusFlowApp 
kotlin.ranges  FocusFlowDatabase 
kotlin.ranges  FocusFlowTheme 
kotlin.ranges  
FontWeight 
kotlin.ranges  	GridCells 
kotlin.ranges  HabitLog 
kotlin.ranges  HabitStreakCard 
kotlin.ranges  HabitStreakItem 
kotlin.ranges  HabitsScreen 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  IllegalArgumentException 
kotlin.ranges  ImpulseControlQuestions 
kotlin.ranges  IncomeSetupStep 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  KeyboardOptions 
kotlin.ranges  KeyboardType 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LazyRow 
kotlin.ranges  LazyVerticalGrid 
kotlin.ranges  LinearProgressIndicator 
kotlin.ranges  LocalContentColor 
kotlin.ranges  LocalContext 
kotlin.ranges  	LocalDate 
kotlin.ranges  
LocalDateTime 
kotlin.ranges  Log 
kotlin.ranges  MainActivity 
kotlin.ranges  MainUiState 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  
MessageBubble 
kotlin.ranges  MessageInputField 
kotlin.ranges  Modifier 
kotlin.ranges  MotivationalQuoteCard 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  NotificationService 
kotlin.ranges  NotificationSetupStep 
kotlin.ranges  NumberFormatException 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  OnboardingProgressIndicator 
kotlin.ranges  OnboardingStep 
kotlin.ranges  OnboardingUiState 
kotlin.ranges  OutlinedButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  Pair 
kotlin.ranges  PayoffCalculation 
kotlin.ranges  
PayoffStep 
kotlin.ranges  PayoffStrategy 
kotlin.ranges  
PendingIntent 
kotlin.ranges  PersonalGoalsStep 
kotlin.ranges  ProgressAchievementsCard 
kotlin.ranges  QuickCategoryOverview 
kotlin.ranges  RadioButton 
kotlin.ranges  Regex 
kotlin.ranges  
RepeatMode 
kotlin.ranges  Room 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  START_NOT_STICKY 
kotlin.ranges  SafeToSpendWidget 
kotlin.ranges  Screen 
kotlin.ranges  SecurityException 
kotlin.ranges  SingletonComponent 
kotlin.ranges  SnackbarDuration 
kotlin.ranges  Spacer 
kotlin.ranges  SpendingSummaryCard 
kotlin.ranges  String 
kotlin.ranges  StringListConverter 
kotlin.ranges  SuggestedPromptCard 
kotlin.ranges  SuggestedPromptsSection 
kotlin.ranges  Surface 
kotlin.ranges  Switch 
kotlin.ranges  System 
kotlin.ranges  Task 
kotlin.ranges  TaskItem 
kotlin.ranges  TasksScreen 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  
TextButton 
kotlin.ranges  TimeZone 
kotlin.ranges  TodaysTasksCard 
kotlin.ranges  Triple 
kotlin.ranges  TypingIndicator 
kotlin.ranges  UserPreferences 
kotlin.ranges  	UserStats 
kotlin.ranges  
VirtualPet 
kotlin.ranges  VirtualPetWidget 
kotlin.ranges  Volatile 
kotlin.ranges  WelcomeStep 
kotlin.ranges  _uiState 
kotlin.ranges  all 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  animateFloatAsState 
kotlin.ranges  apply 
kotlin.ranges  asStateFlow 
kotlin.ranges  atTime 
kotlin.ranges  
background 
kotlin.ranges  bottomNavItems 
kotlin.ranges  budgetCategoryRepository 
kotlin.ranges  	clickable 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  coerceAtMost 
kotlin.ranges  coerceIn 
kotlin.ranges  com 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  contains 
kotlin.ranges  createNotificationChannel 
kotlin.ranges  creditCardRepository 
kotlin.ranges  currentBackStackEntryAsState 
kotlin.ranges  delay 
kotlin.ranges  	emptyList 
kotlin.ranges  expenseRepository 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  finish 
kotlin.ranges  firstOrNull 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  
formatDate 
kotlin.ranges  gamificationService 
kotlin.ranges  generateAIResponse 
kotlin.ranges  getCurrentPeriodDates 
kotlin.ranges  getCurrentPeriodValues 
kotlin.ranges  getValue 
kotlin.ranges  groupBy 
kotlin.ranges  height 
kotlin.ranges  infiniteRepeatable 
kotlin.ranges  invoke 
kotlin.ranges  isBlank 
kotlin.ranges  isDigit 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  kotlinx 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  loadCurrentPeriodExpenses 
kotlin.ranges  loadDebtData 
kotlin.ranges  	lowercase 
kotlin.ranges  	mapValues 
kotlin.ranges  minOf 
kotlin.ranges  minus 
kotlin.ranges  minusAssign 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  mutableStateOf 
kotlin.ranges  none 
kotlin.ranges  padding 
kotlin.ranges  plus 
kotlin.ranges  
plusAssign 
kotlin.ranges  provideDelegate 
kotlin.ranges  random 
kotlin.ranges  remember 
kotlin.ranges  repeat 
kotlin.ranges  replace 
kotlin.ranges  replaceFirstChar 
kotlin.ranges  set 
kotlin.ranges  setValue 
kotlin.ranges  size 
kotlin.ranges  sortedByDescending 
kotlin.ranges  split 
kotlin.ranges  sumOf 
kotlin.ranges  synchronized 
kotlin.ranges  take 
kotlin.ranges  takeIf 
kotlin.ranges  to 
kotlin.ranges  toDouble 
kotlin.ranges  toDoubleOrNull 
kotlin.ranges  toIntOrNull 
kotlin.ranges  toList 
kotlin.ranges  toLocalDateTime 
kotlin.ranges  toMap 
kotlin.ranges  toMutableMap 
kotlin.ranges  toRegex 
kotlin.ranges  trim 
kotlin.ranges  
trimIndent 
kotlin.ranges  tween 
kotlin.ranges  userPreferencesRepository 
kotlin.ranges  width 
kotlin.ranges  widthIn 
kotlin.ranges  contains kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ADHDFriendlyStep kotlin.sequences  
AICoachScreen kotlin.sequences  AICoachUiState kotlin.sequences  
AIInteraction kotlin.sequences  Achievement kotlin.sequences  AchievementBadge kotlin.sequences  	Alignment kotlin.sequences  Arrangement kotlin.sequences  Box kotlin.sequences  BudgetAmountItem kotlin.sequences  BudgetCategory kotlin.sequences  BudgetCategoryItem kotlin.sequences  BudgetOverviewCard kotlin.sequences  BudgetScreen kotlin.sequences  BudgetSetupStep kotlin.sequences  
BudgetUiState kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  
CHANNEL_ID kotlin.sequences  Card kotlin.sequences  ChatMessage kotlin.sequences  Checkbox kotlin.sequences  CircleShape kotlin.sequences  CircularProgressIndicator kotlin.sequences  Clock kotlin.sequences  Color kotlin.sequences  Column kotlin.sequences  CompleteStep kotlin.sequences  CompositionLocalProvider kotlin.sequences  Context kotlin.sequences  
Converters kotlin.sequences  
CreditCard kotlin.sequences  CreditCardItem kotlin.sequences  CreditCardSummaryCard kotlin.sequences  DashboardScreen kotlin.sequences  DashboardUiState kotlin.sequences  DateTimeUnit kotlin.sequences  DebtOverviewCard kotlin.sequences  
DebtScreen kotlin.sequences  
DebtSetupStep kotlin.sequences  DebtUiState kotlin.sequences  Double kotlin.sequences  EmptyBudgetState kotlin.sequences  EmptyStateCard kotlin.sequences  ErrorHandler kotlin.sequences  	Exception kotlin.sequences  Expense kotlin.sequences  ExpenseCategories kotlin.sequences  ExpenseItem kotlin.sequences  ExpenseUiState kotlin.sequences  ExpensesScreen kotlin.sequences  
FilterChip kotlin.sequences  FinancialGoalsStep kotlin.sequences  FloatingActionButton kotlin.sequences  FocusFlowApp kotlin.sequences  FocusFlowDatabase kotlin.sequences  FocusFlowTheme kotlin.sequences  
FontWeight kotlin.sequences  	GridCells kotlin.sequences  HabitLog kotlin.sequences  HabitStreakCard kotlin.sequences  HabitStreakItem kotlin.sequences  HabitsScreen kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  IllegalArgumentException kotlin.sequences  ImpulseControlQuestions kotlin.sequences  IncomeSetupStep kotlin.sequences  Intent kotlin.sequences  KeyboardOptions kotlin.sequences  KeyboardType kotlin.sequences  
LazyColumn kotlin.sequences  LazyRow kotlin.sequences  LazyVerticalGrid kotlin.sequences  LinearProgressIndicator kotlin.sequences  LocalContentColor kotlin.sequences  LocalContext kotlin.sequences  	LocalDate kotlin.sequences  
LocalDateTime kotlin.sequences  Log kotlin.sequences  MainActivity kotlin.sequences  MainUiState kotlin.sequences  
MaterialTheme kotlin.sequences  
MessageBubble kotlin.sequences  MessageInputField kotlin.sequences  Modifier kotlin.sequences  MotivationalQuoteCard kotlin.sequences  MutableStateFlow kotlin.sequences  NOTIFICATION_ID kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  NotificationService kotlin.sequences  NotificationSetupStep kotlin.sequences  NumberFormatException kotlin.sequences  OnConflictStrategy kotlin.sequences  OnboardingProgressIndicator kotlin.sequences  OnboardingStep kotlin.sequences  OnboardingUiState kotlin.sequences  OutlinedButton kotlin.sequences  OutlinedTextField kotlin.sequences  Pair kotlin.sequences  PayoffCalculation kotlin.sequences  
PayoffStep kotlin.sequences  PayoffStrategy kotlin.sequences  
PendingIntent kotlin.sequences  PersonalGoalsStep kotlin.sequences  ProgressAchievementsCard kotlin.sequences  QuickCategoryOverview kotlin.sequences  RadioButton kotlin.sequences  Regex kotlin.sequences  
RepeatMode kotlin.sequences  Room kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  START_NOT_STICKY kotlin.sequences  SafeToSpendWidget kotlin.sequences  Screen kotlin.sequences  SecurityException kotlin.sequences  SingletonComponent kotlin.sequences  SnackbarDuration kotlin.sequences  Spacer kotlin.sequences  SpendingSummaryCard kotlin.sequences  String kotlin.sequences  StringListConverter kotlin.sequences  SuggestedPromptCard kotlin.sequences  SuggestedPromptsSection kotlin.sequences  Surface kotlin.sequences  Switch kotlin.sequences  System kotlin.sequences  Task kotlin.sequences  TaskItem kotlin.sequences  TasksScreen kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  
TextButton kotlin.sequences  TimeZone kotlin.sequences  TodaysTasksCard kotlin.sequences  Triple kotlin.sequences  TypingIndicator kotlin.sequences  UserPreferences kotlin.sequences  	UserStats kotlin.sequences  
VirtualPet kotlin.sequences  VirtualPetWidget kotlin.sequences  Volatile kotlin.sequences  WelcomeStep kotlin.sequences  _uiState kotlin.sequences  all kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  animateFloatAsState kotlin.sequences  apply kotlin.sequences  asStateFlow kotlin.sequences  atTime kotlin.sequences  
background kotlin.sequences  bottomNavItems kotlin.sequences  budgetCategoryRepository kotlin.sequences  	clickable kotlin.sequences  
coerceAtLeast kotlin.sequences  coerceAtMost kotlin.sequences  coerceIn kotlin.sequences  com kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  contains kotlin.sequences  createNotificationChannel kotlin.sequences  creditCardRepository kotlin.sequences  currentBackStackEntryAsState kotlin.sequences  delay kotlin.sequences  	emptyList kotlin.sequences  expenseRepository kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  finish kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  
formatDate kotlin.sequences  gamificationService kotlin.sequences  generateAIResponse kotlin.sequences  getCurrentPeriodDates kotlin.sequences  getCurrentPeriodValues kotlin.sequences  getValue kotlin.sequences  groupBy kotlin.sequences  height kotlin.sequences  infiniteRepeatable kotlin.sequences  invoke kotlin.sequences  isBlank kotlin.sequences  isDigit kotlin.sequences  isEmpty kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  kotlinx kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  loadCurrentPeriodExpenses kotlin.sequences  loadDebtData kotlin.sequences  	lowercase kotlin.sequences  	mapValues kotlin.sequences  minOf kotlin.sequences  minus kotlin.sequences  minusAssign kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  mutableStateOf kotlin.sequences  none kotlin.sequences  padding kotlin.sequences  plus kotlin.sequences  
plusAssign kotlin.sequences  provideDelegate kotlin.sequences  random kotlin.sequences  remember kotlin.sequences  repeat kotlin.sequences  replace kotlin.sequences  replaceFirstChar kotlin.sequences  set kotlin.sequences  setValue kotlin.sequences  size kotlin.sequences  sortedByDescending kotlin.sequences  split kotlin.sequences  sumOf kotlin.sequences  synchronized kotlin.sequences  take kotlin.sequences  takeIf kotlin.sequences  to kotlin.sequences  toDouble kotlin.sequences  toDoubleOrNull kotlin.sequences  toIntOrNull kotlin.sequences  toList kotlin.sequences  toLocalDateTime kotlin.sequences  toMap kotlin.sequences  toMutableMap kotlin.sequences  toRegex kotlin.sequences  trim kotlin.sequences  
trimIndent kotlin.sequences  tween kotlin.sequences  userPreferencesRepository kotlin.sequences  width kotlin.sequences  widthIn kotlin.sequences  ADHDFriendlyStep kotlin.text  
AICoachScreen kotlin.text  AICoachUiState kotlin.text  
AIInteraction kotlin.text  Achievement kotlin.text  AchievementBadge kotlin.text  	Alignment kotlin.text  Arrangement kotlin.text  Box kotlin.text  BudgetAmountItem kotlin.text  BudgetCategory kotlin.text  BudgetCategoryItem kotlin.text  BudgetOverviewCard kotlin.text  BudgetScreen kotlin.text  BudgetSetupStep kotlin.text  
BudgetUiState kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  
CHANNEL_ID kotlin.text  Card kotlin.text  ChatMessage kotlin.text  Checkbox kotlin.text  CircleShape kotlin.text  CircularProgressIndicator kotlin.text  Clock kotlin.text  Color kotlin.text  Column kotlin.text  CompleteStep kotlin.text  CompositionLocalProvider kotlin.text  Context kotlin.text  
Converters kotlin.text  
CreditCard kotlin.text  CreditCardItem kotlin.text  CreditCardSummaryCard kotlin.text  DashboardScreen kotlin.text  DashboardUiState kotlin.text  DateTimeUnit kotlin.text  DebtOverviewCard kotlin.text  
DebtScreen kotlin.text  
DebtSetupStep kotlin.text  DebtUiState kotlin.text  Double kotlin.text  EmptyBudgetState kotlin.text  EmptyStateCard kotlin.text  ErrorHandler kotlin.text  	Exception kotlin.text  Expense kotlin.text  ExpenseCategories kotlin.text  ExpenseItem kotlin.text  ExpenseUiState kotlin.text  ExpensesScreen kotlin.text  
FilterChip kotlin.text  FinancialGoalsStep kotlin.text  FloatingActionButton kotlin.text  FocusFlowApp kotlin.text  FocusFlowDatabase kotlin.text  FocusFlowTheme kotlin.text  
FontWeight kotlin.text  	GridCells kotlin.text  HabitLog kotlin.text  HabitStreakCard kotlin.text  HabitStreakItem kotlin.text  HabitsScreen kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  IllegalArgumentException kotlin.text  ImpulseControlQuestions kotlin.text  IncomeSetupStep kotlin.text  Intent kotlin.text  KeyboardOptions kotlin.text  KeyboardType kotlin.text  
LazyColumn kotlin.text  LazyRow kotlin.text  LazyVerticalGrid kotlin.text  LinearProgressIndicator kotlin.text  LocalContentColor kotlin.text  LocalContext kotlin.text  	LocalDate kotlin.text  
LocalDateTime kotlin.text  Log kotlin.text  MainActivity kotlin.text  MainUiState kotlin.text  
MaterialTheme kotlin.text  
MessageBubble kotlin.text  MessageInputField kotlin.text  Modifier kotlin.text  MotivationalQuoteCard kotlin.text  MutableStateFlow kotlin.text  NOTIFICATION_ID kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  NotificationService kotlin.text  NotificationSetupStep kotlin.text  NumberFormatException kotlin.text  OnConflictStrategy kotlin.text  OnboardingProgressIndicator kotlin.text  OnboardingStep kotlin.text  OnboardingUiState kotlin.text  OutlinedButton kotlin.text  OutlinedTextField kotlin.text  Pair kotlin.text  PayoffCalculation kotlin.text  
PayoffStep kotlin.text  PayoffStrategy kotlin.text  
PendingIntent kotlin.text  PersonalGoalsStep kotlin.text  ProgressAchievementsCard kotlin.text  QuickCategoryOverview kotlin.text  RadioButton kotlin.text  Regex kotlin.text  
RepeatMode kotlin.text  Room kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  START_NOT_STICKY kotlin.text  SafeToSpendWidget kotlin.text  Screen kotlin.text  SecurityException kotlin.text  SingletonComponent kotlin.text  SnackbarDuration kotlin.text  Spacer kotlin.text  SpendingSummaryCard kotlin.text  String kotlin.text  StringListConverter kotlin.text  SuggestedPromptCard kotlin.text  SuggestedPromptsSection kotlin.text  Surface kotlin.text  Switch kotlin.text  System kotlin.text  Task kotlin.text  TaskItem kotlin.text  TasksScreen kotlin.text  Text kotlin.text  	TextAlign kotlin.text  
TextButton kotlin.text  TimeZone kotlin.text  TodaysTasksCard kotlin.text  Triple kotlin.text  TypingIndicator kotlin.text  UserPreferences kotlin.text  	UserStats kotlin.text  
VirtualPet kotlin.text  VirtualPetWidget kotlin.text  Volatile kotlin.text  WelcomeStep kotlin.text  _uiState kotlin.text  all kotlin.text  android kotlin.text  androidx kotlin.text  animateFloatAsState kotlin.text  apply kotlin.text  asStateFlow kotlin.text  atTime kotlin.text  
background kotlin.text  bottomNavItems kotlin.text  budgetCategoryRepository kotlin.text  	clickable kotlin.text  
coerceAtLeast kotlin.text  coerceAtMost kotlin.text  coerceIn kotlin.text  com kotlin.text  
component1 kotlin.text  
component2 kotlin.text  contains kotlin.text  createNotificationChannel kotlin.text  creditCardRepository kotlin.text  currentBackStackEntryAsState kotlin.text  delay kotlin.text  	emptyList kotlin.text  expenseRepository kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  finish kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  format kotlin.text  
formatDate kotlin.text  gamificationService kotlin.text  generateAIResponse kotlin.text  getCurrentPeriodDates kotlin.text  getCurrentPeriodValues kotlin.text  getValue kotlin.text  groupBy kotlin.text  height kotlin.text  infiniteRepeatable kotlin.text  invoke kotlin.text  isBlank kotlin.text  isDigit kotlin.text  isEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  joinToString kotlin.text  kotlinx kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  loadCurrentPeriodExpenses kotlin.text  loadDebtData kotlin.text  	lowercase kotlin.text  	mapValues kotlin.text  minOf kotlin.text  minus kotlin.text  minusAssign kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  mutableStateOf kotlin.text  none kotlin.text  padding kotlin.text  plus kotlin.text  
plusAssign kotlin.text  provideDelegate kotlin.text  random kotlin.text  remember kotlin.text  repeat kotlin.text  replace kotlin.text  replaceFirstChar kotlin.text   replaceFirstCharWithCharSequence kotlin.text  set kotlin.text  setValue kotlin.text  size kotlin.text  sortedByDescending kotlin.text  split kotlin.text  sumOf kotlin.text  synchronized kotlin.text  take kotlin.text  takeIf kotlin.text  to kotlin.text  toDouble kotlin.text  toDoubleOrNull kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toLocalDateTime kotlin.text  toMap kotlin.text  toMutableMap kotlin.text  toRegex kotlin.text  trim kotlin.text  
trimIndent kotlin.text  tween kotlin.text  userPreferencesRepository kotlin.text  width kotlin.text  widthIn kotlin.text  invoke kotlin.text.Regex.Companion  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  BudgetCategory !kotlinx.coroutines.CoroutineScope  ChatMessage !kotlinx.coroutines.CoroutineScope  Clock !kotlinx.coroutines.CoroutineScope  
CreditCard !kotlinx.coroutines.CoroutineScope  DateTimeUnit !kotlinx.coroutines.CoroutineScope  ErrorHandler !kotlinx.coroutines.CoroutineScope  Expense !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TimeZone !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  budgetCategoryRepository !kotlinx.coroutines.CoroutineScope  
coerceAtLeast !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  creditCardRepository !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  expenseRepository !kotlinx.coroutines.CoroutineScope  gamificationService !kotlinx.coroutines.CoroutineScope  generateAIResponse !kotlinx.coroutines.CoroutineScope  getBUDGETCategoryRepository !kotlinx.coroutines.CoroutineScope  getBudgetCategoryRepository !kotlinx.coroutines.CoroutineScope  getCOERCEAtLeast !kotlinx.coroutines.CoroutineScope  getCOM !kotlinx.coroutines.CoroutineScope  getCREDITCardRepository !kotlinx.coroutines.CoroutineScope  getCoerceAtLeast !kotlinx.coroutines.CoroutineScope  getCom !kotlinx.coroutines.CoroutineScope  getCreditCardRepository !kotlinx.coroutines.CoroutineScope  getCurrentPeriodDates !kotlinx.coroutines.CoroutineScope  getCurrentPeriodValues !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  getEXPENSERepository !kotlinx.coroutines.CoroutineScope  getExpenseRepository !kotlinx.coroutines.CoroutineScope  getGAMIFICATIONService !kotlinx.coroutines.CoroutineScope  getGETCurrentPeriodDates !kotlinx.coroutines.CoroutineScope  getGETCurrentPeriodValues !kotlinx.coroutines.CoroutineScope  getGamificationService !kotlinx.coroutines.CoroutineScope  getGenerateAIResponse !kotlinx.coroutines.CoroutineScope  getGetCurrentPeriodDates !kotlinx.coroutines.CoroutineScope  getGetCurrentPeriodValues !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLOADCurrentPeriodExpenses !kotlinx.coroutines.CoroutineScope  getLOADDebtData !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getLoadCurrentPeriodExpenses !kotlinx.coroutines.CoroutineScope  getLoadDebtData !kotlinx.coroutines.CoroutineScope  getPLUS !kotlinx.coroutines.CoroutineScope  getPlus !kotlinx.coroutines.CoroutineScope  getSUMOf !kotlinx.coroutines.CoroutineScope  getSumOf !kotlinx.coroutines.CoroutineScope  getTOLocalDateTime !kotlinx.coroutines.CoroutineScope  getToLocalDateTime !kotlinx.coroutines.CoroutineScope  getUSERPreferencesRepository !kotlinx.coroutines.CoroutineScope  getUserPreferencesRepository !kotlinx.coroutines.CoroutineScope  get_uiState !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadCurrentPeriodExpenses !kotlinx.coroutines.CoroutineScope  loadDebtData !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  sumOf !kotlinx.coroutines.CoroutineScope  toLocalDateTime !kotlinx.coroutines.CoroutineScope  userPreferencesRepository !kotlinx.coroutines.CoroutineScope  AICoachUiState kotlinx.coroutines.flow  BudgetCategory kotlinx.coroutines.flow  
BudgetUiState kotlinx.coroutines.flow  ChatMessage kotlinx.coroutines.flow  Clock kotlinx.coroutines.flow  
CreditCard kotlinx.coroutines.flow  DashboardUiState kotlinx.coroutines.flow  DateTimeUnit kotlinx.coroutines.flow  DebtUiState kotlinx.coroutines.flow  Double kotlinx.coroutines.flow  	Exception kotlinx.coroutines.flow  Expense kotlinx.coroutines.flow  ExpenseUiState kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  	LocalDate kotlinx.coroutines.flow  
LocalDateTime kotlinx.coroutines.flow  MainUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  OnboardingStep kotlinx.coroutines.flow  OnboardingUiState kotlinx.coroutines.flow  Pair kotlinx.coroutines.flow  PayoffCalculation kotlinx.coroutines.flow  
PayoffStep kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  String kotlinx.coroutines.flow  System kotlinx.coroutines.flow  TimeZone kotlinx.coroutines.flow  Triple kotlinx.coroutines.flow  _uiState kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  atTime kotlinx.coroutines.flow  budgetCategoryRepository kotlinx.coroutines.flow  
coerceAtLeast kotlinx.coroutines.flow  coerceAtMost kotlinx.coroutines.flow  com kotlinx.coroutines.flow  contains kotlinx.coroutines.flow  creditCardRepository kotlinx.coroutines.flow  delay kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  expenseRepository kotlinx.coroutines.flow  firstOrNull kotlinx.coroutines.flow  format kotlinx.coroutines.flow  gamificationService kotlinx.coroutines.flow  generateAIResponse kotlinx.coroutines.flow  getCurrentPeriodDates kotlinx.coroutines.flow  getCurrentPeriodValues kotlinx.coroutines.flow  invoke kotlinx.coroutines.flow  isBlank kotlinx.coroutines.flow  launch kotlinx.coroutines.flow  listOf kotlinx.coroutines.flow  loadCurrentPeriodExpenses kotlinx.coroutines.flow  loadDebtData kotlinx.coroutines.flow  	lowercase kotlinx.coroutines.flow  minus kotlinx.coroutines.flow  minusAssign kotlinx.coroutines.flow  plus kotlinx.coroutines.flow  
plusAssign kotlinx.coroutines.flow  random kotlinx.coroutines.flow  sumOf kotlinx.coroutines.flow  to kotlinx.coroutines.flow  toDoubleOrNull kotlinx.coroutines.flow  toLocalDateTime kotlinx.coroutines.flow  
trimIndent kotlinx.coroutines.flow  userPreferencesRepository kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  collect kotlinx.coroutines.flow.Flow  collectAsStateWithLifecycle kotlinx.coroutines.flow.Flow  getCOLLECTAsStateWithLifecycle kotlinx.coroutines.flow.Flow  getCollectAsStateWithLifecycle kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  getCOLLECTAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  getCollectAsStateWithLifecycle !kotlinx.coroutines.flow.StateFlow  BudgetCategory kotlinx.datetime  
BudgetUiState kotlinx.datetime  Clock kotlinx.datetime  
CreditCard kotlinx.datetime  DashboardUiState kotlinx.datetime  DateTimeUnit kotlinx.datetime  DebtUiState kotlinx.datetime  Double kotlinx.datetime  	Exception kotlinx.datetime  Expense kotlinx.datetime  ExpenseUiState kotlinx.datetime  Flow kotlinx.datetime  	LocalDate kotlinx.datetime  
LocalDateTime kotlinx.datetime  MutableStateFlow kotlinx.datetime  Pair kotlinx.datetime  PayoffCalculation kotlinx.datetime  
PayoffStep kotlinx.datetime  	StateFlow kotlinx.datetime  TimeZone kotlinx.datetime  Triple kotlinx.datetime  _uiState kotlinx.datetime  asStateFlow kotlinx.datetime  atTime kotlinx.datetime  budgetCategoryRepository kotlinx.datetime  
coerceAtLeast kotlinx.datetime  coerceAtMost kotlinx.datetime  com kotlinx.datetime  creditCardRepository kotlinx.datetime  	emptyList kotlinx.datetime  expenseRepository kotlinx.datetime  gamificationService kotlinx.datetime  getCurrentPeriodDates kotlinx.datetime  getCurrentPeriodValues kotlinx.datetime  invoke kotlinx.datetime  launch kotlinx.datetime  listOf kotlinx.datetime  loadCurrentPeriodExpenses kotlinx.datetime  loadDebtData kotlinx.datetime  minus kotlinx.datetime  minusAssign kotlinx.datetime  plus kotlinx.datetime  
plusAssign kotlinx.datetime  sumOf kotlinx.datetime  to kotlinx.datetime  toLocalDateTime kotlinx.datetime  userPreferencesRepository kotlinx.datetime  viewModelScope kotlinx.datetime  System kotlinx.datetime.Clock  now kotlinx.datetime.Clock.System  DAY kotlinx.datetime.DateTimeUnit  DayBased kotlinx.datetime.DateTimeUnit  MONTH kotlinx.datetime.DateTimeUnit  
MonthBased kotlinx.datetime.DateTimeUnit  DAY 'kotlinx.datetime.DateTimeUnit.Companion  MONTH 'kotlinx.datetime.DateTimeUnit.Companion  getTOLocalDateTime kotlinx.datetime.Instant  getToLocalDateTime kotlinx.datetime.Instant  toLocalDateTime kotlinx.datetime.Instant  atTime kotlinx.datetime.LocalDate  	dayOfWeek kotlinx.datetime.LocalDate  	dayOfYear kotlinx.datetime.LocalDate  equals kotlinx.datetime.LocalDate  	getATTime kotlinx.datetime.LocalDate  	getAtTime kotlinx.datetime.LocalDate  getMINUS kotlinx.datetime.LocalDate  getMinus kotlinx.datetime.LocalDate  getPLUS kotlinx.datetime.LocalDate  getPlus kotlinx.datetime.LocalDate  minus kotlinx.datetime.LocalDate  month kotlinx.datetime.LocalDate  monthNumber kotlinx.datetime.LocalDate  parse kotlinx.datetime.LocalDate  plus kotlinx.datetime.LocalDate  toString kotlinx.datetime.LocalDate  year kotlinx.datetime.LocalDate  invoke $kotlinx.datetime.LocalDate.Companion  parse $kotlinx.datetime.LocalDate.Companion  date kotlinx.datetime.LocalDateTime  parse kotlinx.datetime.LocalDateTime  toString kotlinx.datetime.LocalDateTime  parse (kotlinx.datetime.LocalDateTime.Companion  currentSystemDefault kotlinx.datetime.TimeZone  currentSystemDefault #kotlinx.datetime.TimeZone.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  