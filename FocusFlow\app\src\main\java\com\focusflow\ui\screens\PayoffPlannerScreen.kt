package com.focusflow.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CreditCard
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.CreditCard
import com.focusflow.ui.viewmodel.DebtViewModel
import com.focusflow.ui.viewmodel.PayoffStrategy
import com.focusflow.ui.viewmodel.PayoffPlan
import com.focusflow.ui.viewmodel.PayoffStep

@Composable
fun PayoffPlannerScreen(
    onNavigateBack: () -> Unit,
    viewModel: DebtViewModel = hiltViewModel()
) {
    val creditCards by viewModel.allCreditCards.collectAsStateWithLifecycle(initialValue = emptyList())
    val payoffPlan by viewModel.payoffPlan.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var selectedStrategy by remember { mutableStateOf(PayoffStrategy.AVALANCHE) }
    var extraPayment by remember { mutableStateOf("") }
    var showComparison by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(Icons.Default.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Debt Payoff Planner",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )
            IconButton(
                onClick = { showComparison = !showComparison }
            ) {
                Icon(
                    if (showComparison) Icons.Default.Close else Icons.Default.Add,
                    contentDescription = "Toggle comparison"
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (creditCards.isEmpty()) {
            EmptyStateMessage()
        } else {
            // Strategy Selection
            StrategySelectionCard(
                selectedStrategy = selectedStrategy,
                onStrategySelected = { selectedStrategy = it },
                extraPayment = extraPayment,
                onExtraPaymentChanged = { extraPayment = it },
                onGeneratePlan = {
                    val extra = extraPayment.toDoubleOrNull() ?: 0.0
                    viewModel.generatePayoffPlan(selectedStrategy, extra)
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Results
            if (payoffPlan.steps.isNotEmpty()) {
                PayoffResultsSection(
                    payoffPlan = payoffPlan,
                    showComparison = showComparison,
                    creditCards = creditCards,
                    viewModel = viewModel
                )
            }
        }

        // Error handling
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // TODO: Show snackbar
                viewModel.clearError()
            }
        }
    }
}

@Composable
fun EmptyStateMessage() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.CreditCard,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No Credit Cards",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Add credit cards to create a payoff plan",
                style = MaterialTheme.typography.body2,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun StrategySelectionCard(
    selectedStrategy: PayoffStrategy,
    onStrategySelected: (PayoffStrategy) -> Unit,
    extraPayment: String,
    onExtraPaymentChanged: (String) -> Unit,
    onGeneratePlan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Payoff Strategy",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Strategy options
            Column {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStrategy == PayoffStrategy.AVALANCHE,
                        onClick = { onStrategySelected(PayoffStrategy.AVALANCHE) }
                    )
                    Column {
                        Text(
                            text = "Avalanche (Recommended)",
                            style = MaterialTheme.typography.body1,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Pay highest interest rate first - saves more money",
                            style = MaterialTheme.typography.caption,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedStrategy == PayoffStrategy.SNOWBALL,
                        onClick = { onStrategySelected(PayoffStrategy.SNOWBALL) }
                    )
                    Column {
                        Text(
                            text = "Snowball",
                            style = MaterialTheme.typography.body1,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Pay smallest balance first - builds momentum",
                            style = MaterialTheme.typography.caption,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Extra payment input
            OutlinedTextField(
                value = extraPayment,
                onValueChange = onExtraPaymentChanged,
                label = { Text("Extra Monthly Payment") },
                placeholder = { Text("0.00") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.fillMaxWidth(),
                leadingIcon = {
                    Text(
                        text = "$",
                        style = MaterialTheme.typography.body1,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Generate plan button
            Button(
                onClick = onGeneratePlan,
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(8.dp)
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Generate Payoff Plan")
            }
        }
    }
}

@Composable
fun PayoffResultsSection(
    payoffPlan: PayoffPlan,
    showComparison: Boolean,
    creditCards: List<CreditCard>,
    viewModel: DebtViewModel
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            PayoffSummaryCard(payoffPlan = payoffPlan)
        }
        
        if (showComparison) {
            item {
                StrategyComparisonCard(
                    creditCards = creditCards,
                    viewModel = viewModel
                )
            }
        }
        
        item {
            Text(
                text = "Monthly Payment Schedule",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
        }
        
        items(payoffPlan.steps.take(12)) { step -> // Show first 12 months
            PayoffStepItem(step = step)
        }
        
        if (payoffPlan.steps.size > 12) {
            item {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    elevation = 2.dp,
                    shape = RoundedCornerShape(8.dp),
                    backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
                ) {
                    Text(
                        text = "... and ${payoffPlan.steps.size - 12} more months",
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.body2,
                        textAlign = TextAlign.Center,
                        color = MaterialTheme.colors.primary
                    )
                }
            }
        }
    }
}

@Composable
fun PayoffSummaryCard(payoffPlan: PayoffPlan) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = MaterialTheme.colors.primary.copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Payoff Summary",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.primary
            )

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                SummaryItem(
                    label = "Time to Payoff",
                    value = "${payoffPlan.totalMonths} months",
                    color = MaterialTheme.colors.primary
                )
                SummaryItem(
                    label = "Total Interest",
                    value = "$${String.format("%.2f", payoffPlan.totalInterestPaid)}",
                    color = Color(0xFFFF9800)
                )
                SummaryItem(
                    label = "Total Payments",
                    value = "$${String.format("%.2f", payoffPlan.totalPayments)}",
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
fun SummaryItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        Text(
            text = value,
            style = MaterialTheme.typography.body1,
            fontWeight = FontWeight.Bold,
            color = color,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
fun StrategyComparisonCard(
    creditCards: List<CreditCard>,
    viewModel: DebtViewModel
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Strategy Comparison",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            // Calculate both strategies for comparison
            val totalDebt = creditCards.sumOf { it.currentBalance }
            val totalMinPayments = creditCards.sumOf { it.minimumPayment }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Avalanche",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colors.primary
                    )
                    Text(
                        text = "Saves more money",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Snowball",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xFFFF9800)
                    )
                    Text(
                        text = "Builds momentum",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
fun PayoffStepItem(step: PayoffStep) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp),
        backgroundColor = if (step.isExtraPayment) {
            MaterialTheme.colors.primary.copy(alpha = 0.1f)
        } else {
            MaterialTheme.colors.surface
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Month ${step.month}",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
                Text(
                    text = step.cardName,
                    style = MaterialTheme.typography.body2,
                    fontWeight = FontWeight.Medium
                )
                if (step.interestPaid > 0) {
                    Text(
                        text = "Interest: $${String.format("%.2f", step.interestPaid)}",
                        style = MaterialTheme.typography.caption,
                        color = Color(0xFFFF9800)
                    )
                }
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "$${String.format("%.2f", step.payment)}",
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Bold,
                    color = if (step.isExtraPayment) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
                )
                Text(
                    text = "Balance: $${String.format("%.2f", step.remainingBalance)}",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}
